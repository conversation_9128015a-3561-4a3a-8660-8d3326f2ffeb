import React from 'react';
import {
  Box,
  Container,
  Grid,
  GridItem,
  useBreakpointValue,
} from '@chakra-ui/react';
import { useTheme, useThemeStyles } from '../styles/themes/ThemeContext';
import ProfileCard from './ProfileCard';
import ThemeSwitcher from './ThemeSwitcher';
// import LanguageSwitcher from './LanguageSwitcher';
import BottomNavigation from './BottomNavigation';
import type { PropsWithChildren } from 'react';

export const Layout = ({ children }: PropsWithChildren) => {
  const { currentTheme } = useTheme();
  const { cardStyle } = useThemeStyles();
  const isDesktop = useBreakpointValue({ base: false, lg: true });
  const showMobileNav = useBreakpointValue({ base: true, lg: false });

  return (
    <Box
      minH="100vh"
      position="relative"
      overflow="hidden"
      pb={showMobileNav ? '80px' : 0}
      sx={{
        '&::before': {
          content: '""',
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          bg: currentTheme.colors.bg.primary,
          backgroundImage: currentTheme.patterns.background,
          transition: 'all 0.3s ease-in-out',
          zIndex: -3
        }
      }}
    >
      {/* Theme and Language Switchers */}
      <Container maxW="container.xl" pt={4}>
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          gap={4}
          mb={4}
        >
          <Box
            transform="scale(1.2)"
            sx={{
              '& > *': {
                boxShadow: `0 0 20px ${currentTheme.colors.button.bg}`,
              }
            }}
          >
            <ThemeSwitcher />
          </Box>
          {/* Language switcher temporarily disabled
          <Box
            transform="scale(1.1)"
            sx={{
              '& > *': {
                boxShadow: `0 0 15px ${currentTheme.colors.text.accent}`,
              }
            }}
          >
            <LanguageSwitcher size="sm" />
          </Box>
          */}
        </Box>
      </Container>

      <Container maxW="container.xl" py={8}>
        <Grid
          templateColumns={{ base: '1fr', lg: '300px 1fr' }}
          gap={6}
        >
          {/* Profile Card */}
          <GridItem>
            {isDesktop ? (
              <Box
                position="sticky"
                top={8}
                {...cardStyle}
                p={6}
                borderRadius="xl"
                borderWidth="1px"
              >
                <ProfileCard />
              </Box>
            ) : (
              <Box
                {...cardStyle}
                p={4}
                borderRadius="xl"
                borderWidth="1px"
                mb={6}
              >
                <ProfileCard />
              </Box>
            )}
          </GridItem>

          {/* Main Content */}
          <GridItem>
            <Box
              {...cardStyle}
              p={6}
              borderRadius="xl"
              borderWidth="1px"
              minH="calc(100vh - 4rem)"
            >
              {children}
            </Box>
          </GridItem>
        </Grid>
      </Container>

      {/* Animated Background */}
      <Box
        position="fixed"
        top={0}
        left={0}
        right={0}
        bottom={0}
        zIndex={-2}
        overflow="hidden"
        sx={{
          '@keyframes floatCandy': {
            '0%': { transform: 'translate(0, 0) rotate(0deg)' },
            '33%': { transform: 'translate(10px, -10px) rotate(10deg)' },
            '66%': { transform: 'translate(-10px, 10px) rotate(-10deg)' },
            '100%': { transform: 'translate(0, 0) rotate(0deg)' }
          },
          '@keyframes wobbleCandy': {
            '0%, 100%': { transform: 'rotate(0deg) scale(1)' },
            '25%': { transform: 'rotate(10deg) scale(1.1)' },
            '75%': { transform: 'rotate(-10deg) scale(0.9)' }
          },
          '@keyframes spinLollipop': {
            '0%': { transform: 'rotate(0deg)' },
            '100%': { transform: 'rotate(360deg)' }
          },
          '@keyframes bounce': {
            '0%, 100%': { transform: 'translateY(0)' },
            '50%': { transform: 'translateY(-10px)' }
          },
          // Candy sprinkles
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: Array(20).fill(0).map((_, i) => `
              radial-gradient(${3 + Math.floor(i/7)}px ${3 + Math.floor(i/7)}px at ${20 + i * 40}px ${30 + (i % 5) * 40}px, ${
                [currentTheme.colors.button.bg, currentTheme.colors.text.accent, currentTheme.colors.button.hover][i % 3]
              } 50%, rgba(0,0,0,0))`).join(','),
            backgroundRepeat: 'repeat',
            backgroundSize: '400px 200px',
            animation: 'wobbleCandy 4s ease-in-out infinite',
            opacity: 0.5,
            filter: 'blur(0.5px)',
          }
        }}
      >
        {/* Lollipops */}
        <Box
          position="absolute"
          top="15%"
          left="10%"
          width="120px"
          height="120px"
          animation="floatCandy 8s ease-in-out infinite"
          sx={{
            svg: {
              width: '100%',
              height: '100%',
              filter: 'drop-shadow(0 0 10px rgba(255, 105, 180, 0.5))'
            }
          }}
        >
          <svg viewBox="0 0 100 100">
            <circle cx="50" cy="35" r="30" fill={currentTheme.colors.button.bg} />
            <path d="M48 65 L48 100" stroke={currentTheme.colors.button.hover} strokeWidth="8" />
            <circle cx="50" cy="35" r="25" fill={`url(#lollipopGradient)`} />
            <defs>
              <radialGradient id="lollipopGradient">
                <stop offset="0%" stopColor={currentTheme.colors.button.hover} />
                <stop offset="100%" stopColor={currentTheme.colors.button.bg} />
              </radialGradient>
            </defs>
          </svg>
        </Box>
        {/* Wrapped Candy */}
        <Box
          position="absolute"
          bottom="20%"
          right="15%"
          width="100px"
          height="60px"
          animation="wobbleCandy 6s ease-in-out infinite"
          sx={{
            svg: {
              width: '100%',
              height: '100%',
              filter: 'drop-shadow(0 0 8px rgba(255, 160, 122, 0.5))'
            }
          }}
        >
          <svg viewBox="0 0 100 60">
            <path
              d="M10 30 Q50 0 90 30 Q50 60 10 30"
              fill={currentTheme.colors.text.accent}
            />
            <path
              d="M20 30 Q50 10 80 30 Q50 50 20 30"
              fill={currentTheme.colors.button.hover}
            />
          </svg>
        </Box>
        {/* Ice Cream */}
        <Box
          position="absolute"
          top="30%"
          right="20%"
          width="80px"
          height="120px"
          animation="floatCandy 7s ease-in-out infinite"
          sx={{
            svg: {
              width: '100%',
              height: '100%',
              filter: 'drop-shadow(0 0 12px rgba(255, 182, 193, 0.6))'
            }
          }}
        >
          <svg viewBox="0 0 80 120">
            <path
              d="M20 50 Q40 0 60 50 L40 110 L20 50"
              fill={currentTheme.colors.card.border}
            />
            <path
              d="M35 105 L45 105 L40 120 Z"
              fill={currentTheme.colors.button.bg}
            />
          </svg>
        </Box>
        {/* Another Lollipop */}
        <Box
          position="absolute"
          top="60%"
          left="25%"
          width="100px"
          height="100px"
          animation="floatCandy 7s ease-in-out infinite"
          sx={{
            svg: {
              width: '100%',
              height: '100%',
              filter: 'drop-shadow(0 0 10px rgba(255, 160, 122, 0.5))'
            }
          }}
        >
          <svg viewBox="0 0 100 100">
            <circle cx="50" cy="35" r="30" fill={currentTheme.colors.text.accent} />
            <path d="M48 65 L48 100" stroke={currentTheme.colors.button.hover} strokeWidth="8" />
            <circle cx="50" cy="35" r="25" fill={currentTheme.colors.button.hover} />
          </svg>
        </Box>
        {/* Another Ice Cream */}
        <Box
          position="absolute"
          bottom="30%"
          left="30%"
          width="70px"
          height="100px"
          animation="floatCandy 6s ease-in-out infinite reverse"
          sx={{
            svg: {
              width: '100%',
              height: '100%',
              filter: 'drop-shadow(0 0 12px rgba(255, 105, 180, 0.6))'
            }
          }}
        >
          <svg viewBox="0 0 80 120">
            <path
              d="M20 50 Q40 0 60 50 L40 110 L20 50"
              fill={currentTheme.colors.button.hover}
            />
            <path
              d="M35 105 L45 105 L40 120 Z"
              fill={currentTheme.colors.text.accent}
            />
          </svg>
        </Box>
      </Box>

      {/* Cotton Candy Clouds */}
      <Box
        position="fixed"
        top="10%"
        left="-5%"
        width="600px"
        height="300px"
        borderRadius="full"
        background={`radial-gradient(circle at center, ${currentTheme.colors.button.bg} 0%, ${currentTheme.colors.bg.secondary} 70%, transparent 100%)`}
        filter="blur(80px)"
        opacity={0.25}
        zIndex={-1}
        transform="rotate(-5deg)"
        animation="floatCandy 15s ease-in-out infinite"
      />
      <Box
        position="fixed"
        bottom="-5%"
        right="-10%"
        width="500px"
        height="400px"
        borderRadius="full"
        background={`radial-gradient(circle at center, ${currentTheme.colors.text.accent} 0%, ${currentTheme.colors.button.hover} 60%, transparent 100%)`}
        filter="blur(100px)"
        opacity={0.2}
        zIndex={-1}
        transform="rotate(10deg)"
        animation="floatCandy 12s ease-in-out infinite reverse"
      />
      <Box
        position="fixed"
        top="40%"
        right="20%"
        width="300px"
        height="200px"
        borderRadius="full"
        background={`radial-gradient(circle at center, ${currentTheme.colors.button.hover} 0%, ${currentTheme.colors.bg.pattern} 70%, transparent 100%)`}
        filter="blur(60px)"
        opacity={0.15}
        zIndex={-1}
        transform="rotate(-15deg)"
        animation="floatCandy 10s ease-in-out infinite 1s"
      />

      {/* Bottom Navigation */}
      <BottomNavigation />
    </Box>
  );
};

export default Layout;