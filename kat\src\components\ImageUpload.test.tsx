import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ChakraProvider } from '@chakra-ui/react';
import theme from '../styles/theme';
import ImageUpload from './ImageUpload';

const renderWithChakra = (ui: React.ReactElement) => {
  return render(
    <ChakraProvider theme={theme}>{ui}</ChakraProvider>
  );
};

describe('ImageUpload', () => {
  const mockOnImageSelect = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the upload button and area', () => {
    renderWithChakra(<ImageUpload onImageSelect={mockOnImageSelect} />);
    expect(screen.getByText('Click to upload image')).toBeInTheDocument();
    expect(screen.getByText('Upload Image')).toBeInTheDocument();
  });

  it('shows preview when currentImage is provided', () => {
    const testImage = 'data:image/jpeg;base64,test-image-data';
    renderWithChakra(
      <ImageUpload onImageSelect={mockOnImageSelect} currentImage={testImage} />
    );
    
    const imageElement = screen.getByAltText('Profile preview');
    expect(imageElement).toBeInTheDocument();
    expect(imageElement).toHaveAttribute('src', testImage);
  });

  it('shows change image button when image is selected', () => {
    const testImage = 'data:image/jpeg;base64,test-image-data';
    renderWithChakra(
      <ImageUpload onImageSelect={mockOnImageSelect} currentImage={testImage} />
    );
    
    expect(screen.getByText('Change Image')).toBeInTheDocument();
  });

  it('handles file selection', async () => {
    renderWithChakra(<ImageUpload onImageSelect={mockOnImageSelect} />);
    
    const file = new File(['test image content'], 'test.jpg', { type: 'image/jpeg' });
    const input = screen.getByTestId('file-input');

    Object.defineProperty(input, 'files', {
      value: [file]
    });

    fireEvent.change(input);

    await waitFor(() => {
      expect(mockOnImageSelect).toHaveBeenCalled();
    });
  });

  it('shows error for invalid file type', async () => {
    renderWithChakra(<ImageUpload onImageSelect={mockOnImageSelect} />);
    
    const file = new File(['test content'], 'test.txt', { type: 'text/plain' });
    const input = screen.getByTestId('file-input');

    Object.defineProperty(input, 'files', {
      value: [file]
    });

    fireEvent.change(input);

    await waitFor(() => {
      // Using test ID to find the error message to avoid duplication with toast
      expect(screen.getByTestId('error-message')).toHaveTextContent('Please upload an image file (PNG, JPEG, etc.)');
    });
  });
});