{"name": "@types/i18next", "version": "12.1.0", "description": "TypeScript definitions for i18next", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/mxl", "githubUsername": "mxl"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/deerawan", "githubUsername": "<PERSON>awan"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/lenovouser", "githubUsername": "lenovouser"}, {"name": "<PERSON>", "url": "https://github.com/qqilihq", "githubUsername": "qqilihq"}, {"name": "Milan Konir", "url": "https://github.com/butchyyyy", "githubUsername": "butchyyyy"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "f88b8110fc629356f179accf2aefd8d2ebd3de70ed96c03493140b12345f91ab", "typeScriptVersion": "2.3"}