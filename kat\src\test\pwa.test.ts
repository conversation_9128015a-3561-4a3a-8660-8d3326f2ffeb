import { describe, test, expect, vi, beforeEach } from 'vitest';

describe('PWA Functionality', () => {
  beforeEach(() => {
    vi.resetModules();
    vi.clearAllMocks();
  });

  test('service worker registration', async () => {
    const registration = {
      installing: null,
      waiting: null,
      active: { state: 'activated' },
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      update: vi.fn(),
    };

    // Mock service worker registration
    const registerMock = vi.fn().mockResolvedValue(registration);
    vi.stubGlobal('navigator', {
      serviceWorker: {
        register: registerMock,
        ready: Promise.resolve(registration),
        controller: { state: 'activated' },
      },
    });

    // Trigger service worker registration (this would normally happen in your main.tsx)
    if ('serviceWorker' in navigator) {
      await navigator.serviceWorker.register('/sw.js');
    }

    expect(registerMock).toHaveBeenCalledWith('/sw.js');
  });

  test('cache storage functionality', async () => {
    const mockCache = {
      put: vi.fn(),
      match: vi.fn(),
      delete: vi.fn(),
    };

    const mockCacheStorage = {
      open: vi.fn().mockResolvedValue(mockCache),
      has: vi.fn().mockResolvedValue(true),
      delete: vi.fn(),
    };

    vi.stubGlobal('caches', mockCacheStorage);

    // Test cache opening
    await caches.open('app-cache-v1');
    expect(mockCacheStorage.open).toHaveBeenCalledWith('app-cache-v1');

    // Test cache checking
    const hasCaches = await caches.has('app-cache-v1');
    expect(hasCaches).toBe(true);
  });

  test('app manifest is properly configured', () => {
    // We can access the manifest through the link tag
    const manifestLink = document.querySelector('link[rel="manifest"]');
    expect(manifestLink).toBeTruthy();
    expect(manifestLink?.getAttribute('href')).toBeDefined();
  });

  test('offline functionality', async () => {
    const mockCache = {
      match: vi.fn().mockResolvedValue(new Response('cached response')),
      put: vi.fn(),
    };

    vi.stubGlobal('caches', {
      open: vi.fn().mockResolvedValue(mockCache),
    });

    // Simulate offline request
    const response = await caches.open('app-cache-v1')
      .then(cache => cache.match('/index.html'));

    expect(response).toBeDefined();
    expect(await response?.text()).toBe('cached response');
  });

  test('app installation prompt', () => {
    let deferredPrompt: any;
    const beforeInstallPromptHandler = (e: Event) => {
      e.preventDefault();
      deferredPrompt = e;
    };

    // Mock beforeinstallprompt event
    const event = new Event('beforeinstallprompt');
    window.addEventListener('beforeinstallprompt', beforeInstallPromptHandler);
    window.dispatchEvent(event);

    expect(deferredPrompt).toBeDefined();
  });

  test('cache cleanup on new version', async () => {
    const mockCacheStorage = {
      keys: vi.fn().mockResolvedValue(['old-cache-v1', 'old-cache-v2']),
      delete: vi.fn().mockResolvedValue(true),
    };

    vi.stubGlobal('caches', mockCacheStorage);

    // Simulate activation event that cleans up old caches
    const cachesToKeep = ['app-cache-v1'];
    const keys = await caches.keys();
    for (const key of keys) {
      if (!cachesToKeep.includes(key)) {
        await caches.delete(key);
      }
    }

    expect(mockCacheStorage.keys).toHaveBeenCalled();
    expect(mockCacheStorage.delete).toHaveBeenCalledWith('old-cache-v1');
    expect(mockCacheStorage.delete).toHaveBeenCalledWith('old-cache-v2');
  });

  test('app updates handling', async () => {
    const mockServiceWorker = {
      state: 'activated',
      postMessage: vi.fn(),
    };

    const registration = {
      installing: null,
      waiting: mockServiceWorker,
      active: { state: 'activated' },
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      update: vi.fn(),
    };

    vi.stubGlobal('navigator', {
      serviceWorker: {
        register: vi.fn().mockResolvedValue(registration),
        ready: Promise.resolve(registration),
        controller: mockServiceWorker,
      },
    });

    // Simulate update found
    const updateHandler = vi.fn();
    registration.addEventListener('updatefound', updateHandler);
    
    // Trigger update check
    await registration.update();
    
    expect(registration.update).toHaveBeenCalled();
  });
});