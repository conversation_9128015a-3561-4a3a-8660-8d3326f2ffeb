import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { withCache } from '../utils/cache';
import { selectAchievements, selectUserAchievements } from '../store/slices/achievementSlice';
import { RootState } from '../store';
import { Achievement, UserAchievement } from '../types/achievement';
import { useLoadingState } from './useLoadingState';

interface AchievementData {
  achievements: Achievement[];
  userAchievements: UserAchievement[];
  achievementStats: {
    total: number;
    unlocked: number;
    percentComplete: number;
  };
  isLoading: boolean;
  error: Error | null;
}

const CACHE_KEY_PREFIX = 'achievement-data';
const CACHE_MAX_AGE = 5 * 60 * 1000; // 5 minutes

export function useAchievementData(profileId: string): AchievementData {
  const achievements = useSelector(selectAchievements);
  const userAchievements = useSelector(selectUserAchievements);
  const [error, setError] = useState<Error | null>(null);
  const { setLoading } = useLoadingState();

  const [achievementStats, setAchievementStats] = useState({
    total: 0,
    unlocked: 0,
    percentComplete: 0,
  });

  useEffect(() => {
    async function fetchAchievementData() {
      if (!profileId) return;

      const cacheKey = `${CACHE_KEY_PREFIX}-${profileId}`;
      setLoading(true);

      try {
        const stats = await withCache(
          cacheKey,
          async () => {
            // Simulate some processing time to make caching worthwhile
            await new Promise(resolve => setTimeout(resolve, 100));

            const unlockedAchievements = userAchievements.filter(
              ua => ua.profileId === profileId
            );

            const total = achievements.length;
            const unlocked = unlockedAchievements.length;
            const percentComplete = Math.round((unlocked / total) * 100) || 0;

            return {
              total,
              unlocked,
              percentComplete,
            };
          },
          { maxAge: CACHE_MAX_AGE }
        );

        setAchievementStats(stats);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to load achievement data'));
      } finally {
        setLoading(false);
      }
    }

    fetchAchievementData();
  }, [profileId, achievements, userAchievements, setLoading]);

  const filteredUserAchievements = userAchievements.filter(
    ua => ua.profileId === profileId
  );

  return {
    achievements,
    userAchievements: filteredUserAchievements,
    achievementStats,
    isLoading: false, // We're using global loading state
    error,
  };
}