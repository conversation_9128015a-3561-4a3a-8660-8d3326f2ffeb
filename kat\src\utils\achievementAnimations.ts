import confetti from 'canvas-confetti';

export const triggerAchievementAnimation = () => {
  const duration = 2000;
  const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 10000 };

  // Launch confetti from both sides
  const animationEnd = Date.now() + duration;

  const interval = setInterval(() => {
    const timeLeft = animationEnd - Date.now();

    if (timeLeft <= 0) {
      clearInterval(interval);
      return;
    }

    const particleCount = 50 * (timeLeft / duration);

    // Launch from left
    confetti({
      ...defaults,
      particleCount,
      origin: { x: 0.2, y: 0.5 },
      colors: ['#FFD700', '#FFA500', '#FF6B6B'],
    });

    // Launch from right
    confetti({
      ...defaults,
      particleCount,
      origin: { x: 0.8, y: 0.5 },
      colors: ['#FFD700', '#FFA500', '#FF6B6B'],
    });
  }, 250);
};

export const getProgressPercentage = (
  currentValue: number,
  targetValue: number
): number => {
  return Math.min(Math.round((currentValue / targetValue) * 100), 100);
};