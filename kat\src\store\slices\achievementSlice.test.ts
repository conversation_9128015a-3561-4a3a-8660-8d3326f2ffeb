import { describe, it, expect, beforeEach, vi } from 'vitest';

// Move mocks to top before any imports
vi.mock('../../utils/storage', async () => {
  const actual = await vi.importActual('../../utils/storage') as typeof import('../../utils/storage');
  return {
    ...actual,
    StorageManager: {
      get: vi.fn().mockImplementation((key: string, defaultValue: any) => defaultValue),
      set: vi.fn(),
    },
  };
});

vi.mock('../../utils/sounds', () => ({
  playSound: vi.fn(),
}));

import achievementReducer, {
  unlockAchievement,
  checkTasksCompletedAchievements,
  checkPointsEarnedAchievements,
  checkStreakAchievements,
} from './achievementSlice';
import { ACHIEVEMENTS } from '../../features/achievements/predefinedAchievements';
import { Achievement } from '../../types/achievement';

describe('achievementSlice', () => {
  const initialAchievements = ACHIEVEMENTS.map((achievement) => ({
    ...achievement,
    id: achievement.title.toLowerCase().replace(/\s+/g, '-'),
  }));

  const initialState = {
    achievements: initialAchievements,
    userAchievements: [],
    lastUnlocked: null,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('unlockAchievement', () => {
    it('unlocks a new achievement', () => {
      const state = achievementReducer(
        initialState,
        unlockAchievement({ profileId: 'test-profile', achievementId: 'first-steps' })
      );

      expect(state.userAchievements).toHaveLength(1);
      expect(state.userAchievements[0]).toMatchObject({
        profileId: 'test-profile',
        achievementId: 'first-steps',
      });
      expect(state.lastUnlocked).toMatchObject({
        title: 'First Steps',
        description: 'Complete your first task',
      });
    });

    it('prevents unlocking the same achievement twice', () => {
      let state = achievementReducer(
        initialState,
        unlockAchievement({ profileId: 'test-profile', achievementId: 'first-steps' })
      );

      state = achievementReducer(
        state,
        unlockAchievement({ profileId: 'test-profile', achievementId: 'first-steps' })
      );

      expect(state.userAchievements).toHaveLength(1);
    });
  });

  describe('checkTasksCompletedAchievements', () => {
    it('unlocks task achievements when conditions are met', () => {
      const state = achievementReducer(
        initialState,
        checkTasksCompletedAchievements({
          profileId: 'test-profile',
          completedCount: 10,
        })
      );

      const unlockedAchievements = state.userAchievements.map(ua => ua.achievementId);
      expect(unlockedAchievements).toContain('first-steps'); // 1 task
      expect(unlockedAchievements).toContain('task-master'); // 10 tasks
      expect(unlockedAchievements).not.toContain('super-achiever'); // 50 tasks
    });
  });

  describe('checkPointsEarnedAchievements', () => {
    it('unlocks point achievements when conditions are met', () => {
      const state = achievementReducer(
        initialState,
        checkPointsEarnedAchievements({
          profileId: 'test-profile',
          totalPoints: 100,
        })
      );

      const unlockedAchievements = state.userAchievements.map(ua => ua.achievementId);
      expect(unlockedAchievements).toContain('point-collector'); // 100 points
      expect(unlockedAchievements).not.toContain('point-master'); // 500 points
    });
  });

  describe('checkStreakAchievements', () => {
    it('unlocks streak achievements when conditions are met', () => {
      const state = achievementReducer(
        initialState,
        checkStreakAchievements({
          profileId: 'test-profile',
          streakDays: 7,
        })
      );

      const unlockedAchievements = state.userAchievements.map(ua => ua.achievementId);
      expect(unlockedAchievements).toContain('daily-hero'); // 3-day streak
      expect(unlockedAchievements).toContain('weekly-champion'); // 7-day streak
      expect(unlockedAchievements).not.toContain('monthly-legend'); // 30-day streak
    });
  });
});