import { describe, test, expect } from 'vitest';

describe('Browser Compatibility', () => {
  const requiredApis = [
    'Promise',
    'Set',
    'Map',
    'localStorage',
    'indexedDB',
    'fetch',
    'IntersectionObserver',
    'ResizeObserver',
  ] as const;

  test('required APIs are available', () => {
    requiredApis.forEach(api => {
      expect(window[api as keyof Window]).toBeDefined();
    });
  });

  test('localStorage is functional', () => {
    localStorage.setItem('test', 'value');
    expect(localStorage.getItem('test')).toBe('value');
    localStorage.removeItem('test');
  });

  test('indexedDB is available and functional', () => {
    expect(indexedDB).toBeDefined();
    const request = indexedDB.open('test', 1);
    expect(request).toBeDefined();
  });

  test('service workers are supported', () => {
    expect('serviceWorker' in navigator).toBe(true);
  });

  test('modern JavaScript features are supported', () => {
    // ES6+ features
    expect(() => {
      const asyncFn = async () => {};
      const generator = function* () {};
      const symbol = Symbol('test');
      const map = new Map();
      const set = new Set();
      const proxy = new Proxy({}, {});
      const weakMap = new WeakMap();
      const weakSet = new WeakSet();
      const bigInt = BigInt(1);
    }).not.toThrow();
  });

  test('modern DOM APIs are supported', () => {
    expect(window.MutationObserver).toBeDefined();
    expect(window.IntersectionObserver).toBeDefined();
    expect(window.ResizeObserver).toBeDefined();
    expect(window.requestAnimationFrame).toBeDefined();
    expect(window.matchMedia).toBeDefined();
  });

  test('modern CSS features are supported', () => {
    const div = document.createElement('div');
    const supportsFlex = 'flex' in div.style;
    const supportsGrid = 'grid' in div.style;
    const supportsCustomProperties = CSS.supports('--custom-property', 'value');

    expect(supportsFlex).toBe(true);
    expect(supportsGrid).toBe(true);
    expect(supportsCustomProperties).toBe(true);
  });

  test('WebP image format is supported', async () => {
    const webpSupported = () => {
      return new Promise(resolve => {
        const webp = new Image();
        webp.onload = () => resolve(true);
        webp.onerror = () => resolve(false);
        webp.src = 'data:image/webp;base64,UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA==';
      });
    };

    expect(await webpSupported()).toBe(true);
  });

  test('Web APIs for PWA are supported', () => {
    expect('serviceWorker' in navigator).toBe(true);
    expect('caches' in window).toBe(true);
    
    // Check for PWA-related APIs
    interface PWAWindow extends Window {
      Notification?: Function;
      PushManager?: Function;
      BeforeInstallPromptEvent?: Function;
    }

    const pwaApis = [
      'Notification',
      'PushManager',
      'BeforeInstallPromptEvent'
    ] as const;

    pwaApis.forEach(api => {
      // Some APIs might be undefined in test environment, so we don't fail the test
      expect(['function', 'object', 'undefined']).toContain(typeof (window as PWAWindow)[api]);
    });
  });
});