import { vi, describe, test, expect, beforeEach } from 'vitest';
import { cache, withCache } from './cache';

// Mock indexedDB
const indexedDB = {
  open: vi.fn(),
  deleteDatabase: vi.fn(),
};

global.indexedDB = indexedDB as any;

describe('Cache', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    cache.clear(); // Clear the cache before each test
  });

  test('should store and retrieve data', async () => {
    const testKey = 'test-key';
    const testData = { foo: 'bar' };

    await cache.set(testKey, testData);
    const retrieved = await cache.get(testKey);

    expect(retrieved).toEqual(testData);
  });

  test('should handle expired data', async () => {
    const testKey = 'test-key';
    const testData = { foo: 'bar' };
    const maxAge = 100; // 100ms

    await cache.set(testKey, testData);
    
    // Wait for data to expire
    await new Promise(resolve => setTimeout(resolve, maxAge + 50));
    
    const retrieved = await cache.get(testKey, maxAge);
    expect(retrieved).toBeNull();
  });

  test('should cleanup expired entries', async () => {
    const testData = { foo: 'bar' };
    const maxAge = 100; // 100ms

    await cache.set('key1', testData);
    await cache.set('key2', testData);

    // Wait for data to expire
    await new Promise(resolve => setTimeout(resolve, maxAge + 50));

    await cache.cleanup(maxAge);

    const key1Data = await cache.get('key1');
    const key2Data = await cache.get('key2');

    expect(key1Data).toBeNull();
    expect(key2Data).toBeNull();
  });
});

describe('withCache', () => {
  test('should cache fetcher results', async () => {
    const fetcher = vi.fn().mockResolvedValue({ data: 'test' });
    
    // First call should use fetcher
    const result1 = await withCache('test-key', fetcher);
    expect(fetcher).toHaveBeenCalledTimes(1);
    expect(result1).toEqual({ data: 'test' });

    // Second call should use cache
    const result2 = await withCache('test-key', fetcher);
    expect(fetcher).toHaveBeenCalledTimes(1); // Still 1
    expect(result2).toEqual({ data: 'test' });
  });

  test('should respect forceRefresh option', async () => {
    const fetcher = vi.fn().mockResolvedValue({ data: 'test' });
    
    // First call
    await withCache('test-key', fetcher);
    expect(fetcher).toHaveBeenCalledTimes(1);

    // Force refresh
    await withCache('test-key', fetcher, { forceRefresh: true });
    expect(fetcher).toHaveBeenCalledTimes(2);
  });

  test('should handle errors gracefully', async () => {
    const error = new Error('Fetch failed');
    const fetcher = vi.fn().mockRejectedValue(error);

    await expect(withCache('test-key', fetcher)).rejects.toBe(error);
    expect(fetcher).toHaveBeenCalledTimes(1);
  });
});