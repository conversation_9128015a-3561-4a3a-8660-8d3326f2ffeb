import React, { useEffect, useState } from 'react';
import {
  <PERSON>dal,
  ModalOverlay,
  Modal<PERSON>ontent,
  ModalHeader,
  ModalBody,
  Modal<PERSON>ooter,
  Button,
  Text,
  VStack,
  useDisclosure,
  Link,
} from '@chakra-ui/react';
import { analytics } from '../utils/analytics';

const AnalyticsConsent: React.FC = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [hasInteracted, setHasInteracted] = useState(false);

  useEffect(() => {
    // Check if user has made a choice before
    const hasChoice = localStorage.getItem('analytics_choice') === 'true';
    if (!hasChoice && !hasInteracted) {
      onOpen();
    }
  }, [onOpen, hasInteracted]);

  const handleAccept = () => {
    analytics.enable();
    localStorage.setItem('analytics_choice', 'true');
    setHasInteracted(true);
    onClose();
  };

  const handleDecline = () => {
    analytics.disable();
    localStorage.setItem('analytics_choice', 'true');
    setHasInteracted(true);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      closeOnOverlayClick={false}
      closeOnEsc={false}
      isCentered
    >
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Help Us Improve</ModalHeader>
        <ModalBody>
          <VStack spacing={4} align="stretch">
            <Text>
              We'd like to collect anonymous usage data to improve the Kids Activity Tracker. This includes:
            </Text>
            <VStack spacing={2} align="start" pl={4}>
              <Text>• Basic app interactions</Text>
              <Text>• Achievement statistics</Text>
              <Text>• Error reports</Text>
            </VStack>
            <Text>
              We don't collect any personal information or track individual users. Your data stays private and secure.
            </Text>
            <Text fontSize="sm" color="gray.500">
              You can change this choice anytime in settings. Read our{' '}
              <Link color="primary.500" href="/privacy" isExternal>
                Privacy Policy
              </Link>
              {' '}for more details.
            </Text>
          </VStack>
        </ModalBody>

        <ModalFooter gap={4}>
          <Button variant="outline" onClick={handleDecline}>
            Decline
          </Button>
          <Button colorScheme="primary" onClick={handleAccept}>
            Accept
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default AnalyticsConsent;