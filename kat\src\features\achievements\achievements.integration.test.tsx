import React from 'react';
import { describe, test, expect, vi, beforeEach } from 'vitest';
import { render, screen, act } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import achievementReducer, {
  unlockAchievement,
} from '../../store/slices/achievementSlice';
import { LoadingProvider } from '../../hooks/useLoadingState';
import AchievementList from './AchievementList';
import AchievementNotification from './AchievementNotification';
import { ACHIEVEMENTS } from './predefinedAchievements';
import { createAchievement, type Achievement } from '../../types/achievement';

// Mock sounds
vi.mock('../../utils/sounds', () => ({
  playSound: vi.fn(),
}));

// Mock animations
vi.mock('../../utils/achievementAnimations', () => ({
  triggerAchievementAnimation: vi.fn(),
  getProgressPercentage: vi.fn((current, target) => (current / target) * 100),
}));

// Convert predefined achievements to full achievements with IDs
const testAchievements: Achievement[] = ACHIEVEMENTS.map(createAchievement);

const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      achievement: achievementReducer,
    },
    preloadedState: {
      achievement: {
        achievements: testAchievements,
        userAchievements: [],
        lastUnlocked: null,
        ...initialState,
      },
    },
  });
};

const renderAchievementSystem = (store = createTestStore()) => {
  return render(
    <Provider store={store}>
      <LoadingProvider>
        <AchievementList />
        <AchievementNotification />
      </LoadingProvider>
    </Provider>
  );
};

describe('Achievement System Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('should display achievements and track progress', () => {
    const store = createTestStore();
    renderAchievementSystem(store);

    // Should show all achievements
    testAchievements.forEach((achievement) => {
      expect(screen.getByText(achievement.title)).toBeInTheDocument();
      expect(screen.getByText(achievement.description)).toBeInTheDocument();
    });

    // All achievements should be locked initially
    expect(screen.queryByText('Unlocked')).not.toBeInTheDocument();
  });

  test('should show notification when achievement is unlocked', async () => {
    const store = createTestStore();
    renderAchievementSystem(store);

    // Unlock an achievement
    act(() => {
      store.dispatch(
        unlockAchievement({
          profileId: 'test-profile',
          achievementId: testAchievements[0].id,
        })
      );
    });

    // Should show notification
    expect(screen.getByText('Achievement Unlocked!')).toBeInTheDocument();
    expect(screen.getByText(testAchievements[0].title)).toBeInTheDocument();

    // Should update achievement display
    const achievementElement = screen.getByText(testAchievements[0].title)
      .closest('[role="group"]');
    expect(achievementElement).toHaveStyle({
      opacity: '1',
    });
  });

  test('should handle multiple achievements being unlocked', async () => {
    const store = createTestStore();
    renderAchievementSystem(store);

    // Unlock multiple achievements
    act(() => {
      store.dispatch(
        unlockAchievement({
          profileId: 'test-profile',
          achievementId: testAchievements[0].id,
        })
      );
      store.dispatch(
        unlockAchievement({
          profileId: 'test-profile',
          achievementId: testAchievements[1].id,
        })
      );
    });

    // Both achievements should be marked as unlocked
    const unlockedAchievements = screen.getAllByText(/Unlocked/);
    expect(unlockedAchievements).toHaveLength(2);
  });

  test('should show progress for locked achievements', () => {
    const store = createTestStore();
    renderAchievementSystem(store);

    testAchievements.forEach((achievement) => {
      const progressText = screen.getByText(
        achievement.condition.type === 'TASKS_COMPLETED'
          ? `Complete ${achievement.condition.value} tasks`
          : achievement.condition.type === 'POINTS_EARNED'
          ? `Earn ${achievement.condition.value} points`
          : `Maintain a ${achievement.condition.value}-day streak`
      );
      expect(progressText).toBeInTheDocument();
    });
  });

  test('should handle achievement animations and sounds', () => {
    const store = createTestStore();
    renderAchievementSystem(store);

    act(() => {
      store.dispatch(
        unlockAchievement({
          profileId: 'test-profile',
          achievementId: testAchievements[0].id,
        })
      );
    });

    // Should trigger animation and sound
    expect(require('../../utils/achievementAnimations').triggerAchievementAnimation).toHaveBeenCalled();
    expect(require('../../utils/sounds').playSound).toHaveBeenCalledWith('achievement');
  });
});