import { keyframes } from '@chakra-ui/react';

export const fadeIn=keyframes`from{opacity:0}to{opacity:1}`;

export const slideInRight=keyframes`from{transform:translateX(100%);opacity:0}to{transform:translateX(0);opacity:1}`;

export const slideInLeft=keyframes`from{transform:translateX(-100%);opacity:0}to{transform:translateX(0);opacity:1}`;

export const bounceScale=keyframes`0%{transform:scale(1)}40%{transform:scale(1.1)}60%{transform:scale(0.95)}80%{transform:scale(1.05)}100%{transform:scale(1)}`;

export const floatAnimation=keyframes`0%{transform:translateY(0)}25%{transform:translateY(-15px)}75%{transform:translateY(10px)}100%{transform:translateY(0)}`;

export const glowPulse=keyframes`0%{box-shadow:0 0 5px #fff}50%{box-shadow:0 0 20px #fff}100%{box-shadow:0 0 5px #fff}`;

export const rotateGlow=keyframes`0%{transform:rotate(0deg)}50%{transform:rotate(180deg)}100%{transform:rotate(360deg)}`;

export const sparkle=keyframes`0%{filter:brightness(100%)}50%{filter:brightness(130%)}100%{filter:brightness(100%)}`;

export const wobble=keyframes`0%{transform:translateX(0)}15%{transform:translateX(-8px)}30%{transform:translateX(6px)}45%{transform:translateX(-4px)}60%{transform:translateX(2px)}75%{transform:translateX(-1px)}100%{transform:translateX(0)}`;

export const shakeAnimation=keyframes`0%{transform:translateX(0)}25%{transform:translateX(8px)}50%{transform:translateX(-8px)}75%{transform:translateX(4px)}100%{transform:translateX(0)}`;

export const rainbow=keyframes`0%{filter:hue-rotate(0deg)}50%{filter:hue-rotate(180deg)}100%{filter:hue-rotate(360deg)}`;

export const popIn=keyframes`0%{transform:scale(0.5);opacity:0;filter:blur(10px)}50%{transform:scale(1.1);opacity:0.8;filter:blur(5px)}100%{transform:scale(1);opacity:1;filter:blur(0)}`;