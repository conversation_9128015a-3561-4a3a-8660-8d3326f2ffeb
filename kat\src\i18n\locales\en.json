{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "add": "Add", "close": "Close", "confirm": "Confirm", "back": "Back", "next": "Next", "done": "Done", "yes": "Yes", "no": "No", "points": "Points", "level": "Level"}, "navigation": {"home": "Home", "tasks": "Tasks", "achievements": "Achievements", "parent": "Parent", "profile": "Profile"}, "profile": {"title": "Profile", "selectProfile": "Select Profile", "createProfile": "Create Profile", "editProfile": "Edit Profile", "name": "Name", "avatar": "Avatar", "points": "Points", "level": "Level", "enterName": "Enter your name", "nameRequired": "Name is required", "profileCreated": "Profile created successfully!", "profileUpdated": "Profile updated successfully!", "deleteProfile": "Delete Profile", "confirmDelete": "Are you sure you want to delete this profile?", "noProfiles": "No profiles found. Create your first profile!"}, "tasks": {"title": "Tasks", "myTasks": "My Tasks", "createTask": "Create Task", "editTask": "Edit Task", "taskTitle": "Task Title", "description": "Description", "reward": "<PERSON><PERSON>", "assignTo": "Assign To", "completed": "Completed", "pending": "Pending", "markComplete": "Mark Complete", "markIncomplete": "<PERSON>", "deleteTask": "Delete Task", "noTasks": "No tasks found. Create your first task!", "taskCompleted": "Task completed! You earned {{points}} points!", "taskCreated": "Task created successfully!", "taskUpdated": "Task updated successfully!", "taskDeleted": "Task deleted successfully!", "categories": {"chores": "Chores", "homework": "Homework", "selfCare": "Self Care", "helping": "Helping Others", "exercise": "Exercise", "reading": "Reading", "creative": "Creative"}}, "achievements": {"title": "Achievements", "myAchievements": "My Achievements", "unlocked": "Unlocked", "locked": "Locked", "progress": "Progress", "congratulations": "Congratulations!", "achievementUnlocked": "Achievement Unlocked!", "noAchievements": "No achievements unlocked yet. Complete tasks to earn achievements!", "predefined": {"firstSteps": {"title": "First Steps", "description": "Complete your first task"}, "taskMaster": {"title": "Task Master", "description": "Complete 10 tasks"}, "superAchiever": {"title": "Super Achiever", "description": "Complete 50 tasks"}, "pointCollector": {"title": "Point Collector", "description": "<PERSON><PERSON><PERSON> 100 points"}, "pointMaster": {"title": "Point Master", "description": "<PERSON>arn 500 points"}, "streakStarter": {"title": "<PERSON><PERSON> Starter", "description": "Complete tasks for 3 days in a row"}, "streakMaster": {"title": "Streak Master", "description": "Complete tasks for 7 days in a row"}}}, "parent": {"title": "Parent Dashboard", "enterPin": "Enter PIN", "incorrectPin": "Incorrect PIN", "pinRequired": "PIN is required", "unlock": "Unlock", "lock": "Lock", "settings": "Settings", "changePin": "Change PIN", "newPin": "New PIN", "confirmPin": "Confirm PIN", "pinChanged": "PIN changed successfully!", "pinMismatch": "PINs do not match", "features": {"taskCreation": "Task Creation", "profileCreation": "Profile Creation", "rewardRedemption": "Reward Redemption"}, "statistics": {"title": "Statistics", "totalTasks": "Total Tasks", "completedTasks": "Completed Tasks", "totalPoints": "Total Points", "activeProfiles": "Active Profiles"}}, "home": {"welcome": "Welcome back, {{name}}!", "welcomeGeneral": "Welcome to KAT!", "todaysTasks": "Today's Tasks", "recentAchievements": "Recent Achievements", "yourProgress": "Your Progress", "pointsEarned": "Points Earned", "tasksCompleted": "Tasks Completed", "currentStreak": "Current Streak", "days": "days"}, "language": {"title": "Language", "select": "Select Language", "current": "Current Language", "change": "Change Language", "changed": "Language changed successfully!"}, "theme": {"title": "Theme", "light": "Light", "dark": "Dark", "system": "System", "candy": "<PERSON>", "space": "Space", "neon": "Neon"}, "errors": {"generic": "Something went wrong. Please try again.", "network": "Network error. Please check your connection.", "storage": "Storage error. Please try again.", "validation": "Please check your input and try again."}}