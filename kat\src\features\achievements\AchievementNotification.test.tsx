import { render, screen } from '@testing-library/react';
import { <PERSON>kraProvider } from '@chakra-ui/react';
import AchievementNotification from './AchievementNotification';
import { selectLastUnlockedAchievement, clearLastUnlocked } from '../../store/slices/achievementSlice';
import { triggerAchievementAnimation } from '../../utils/achievementAnimations';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import theme from '../../styles/theme';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { act } from '@testing-library/react';

// Mock achievement slice
vi.mock('../../store/slices/achievementSlice', () => ({
  selectLastUnlockedAchievement: vi.fn(),
  clearLastUnlocked: vi.fn(() => ({ type: 'achievements/clearLastUnlocked' }))
}));

// Mock achievement animation function
vi.mock('../../utils/achievementAnimations', () => ({
  triggerAchievementAnimation: vi.fn()
}));

// Create a mock store
const createMockStore = (lastUnlockedAchievement: any = null) => {
  return configureStore({
    reducer: {
      achievements: (state = { lastUnlockedAchievement }) => state
    },
    middleware: (getDefaultMiddleware) => getDefaultMiddleware()
  });
};

describe('AchievementNotification', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('renders nothing when there is no unlocked achievement', () => {
    (selectLastUnlockedAchievement as unknown as ReturnType<typeof vi.fn>).mockReturnValue(null);
    const store = createMockStore();
    
    render(
      <Provider store={store}>
        <ChakraProvider theme={theme}>
          <AchievementNotification />
        </ChakraProvider>
      </Provider>
    );
    
    expect(screen.queryByRole('alert')).not.toBeInTheDocument();
  });

  it('renders achievement notification when achievement is unlocked', () => {
    const achievement = {
      name: 'Test Achievement',
      description: 'Test Description'
    };

    (selectLastUnlockedAchievement as unknown as ReturnType<typeof vi.fn>).mockReturnValue(achievement);
    const store = createMockStore(achievement);
    
    render(
      <Provider store={store}>
        <ChakraProvider theme={theme}>
          <AchievementNotification />
        </ChakraProvider>
      </Provider>
    );
    
    expect(screen.getByRole('alert')).toBeInTheDocument();
    expect(screen.getByText('Achievement Unlocked!')).toBeInTheDocument();
    expect(screen.getByText('Test Achievement')).toBeInTheDocument();
    expect(screen.getByText('Test Description')).toBeInTheDocument();
  });

  it('triggers achievement animation when shown', () => {
    const achievement = {
      name: 'Test Achievement',
      description: 'Test Description'
    };

    (selectLastUnlockedAchievement as unknown as ReturnType<typeof vi.fn>).mockReturnValue(achievement);
    const store = createMockStore(achievement);
    
    render(
      <Provider store={store}>
        <ChakraProvider theme={theme}>
          <AchievementNotification />
        </ChakraProvider>
      </Provider>
    );
    
    expect(triggerAchievementAnimation).toHaveBeenCalled();
  });

  it('automatically dismisses after 3 seconds', () => {
    const achievement = {
      name: 'Test Achievement',
      description: 'Test Description'
    };

    (selectLastUnlockedAchievement as unknown as ReturnType<typeof vi.fn>).mockReturnValue(achievement);
    const store = createMockStore(achievement);
    
    render(
      <Provider store={store}>
        <ChakraProvider theme={theme}>
          <AchievementNotification />
        </ChakraProvider>
      </Provider>
    );
    
    expect(screen.getByText('Achievement Unlocked!')).toBeInTheDocument();
    
    act(() => {
      vi.advanceTimersByTime(3000);
    });
    
    expect(clearLastUnlocked).toHaveBeenCalled();
  });
});