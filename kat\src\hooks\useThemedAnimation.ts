import { useTheme } from '../styles/themes/ThemeContext';
import { keyframes, usePrefersReducedMotion } from '@chakra-ui/react';
import * as animations from '../styles/themes/animations';

type AnimationName = keyof typeof animations;
type AnimationConfig = {
  duration?: string;
  delay?: string;
  iterationCount?: string | number;
  fillMode?: 'none' | 'forwards' | 'backwards' | 'both';
  timingFunction?: string;
};

export const useThemedAnimation = (
  animationName: AnimationName,
  config: AnimationConfig = {}
) => {
  const prefersReducedMotion = usePrefersReducedMotion();
  const { currentTheme } = useTheme();

  const {
    duration = '1s',
    delay = '0s',
    iterationCount = '1',
    fillMode = 'both',
    timingFunction = 'ease',
  } = config;

  if (prefersReducedMotion) {
    return {};
  }

  return {
    animation: `${animations[animationName]} ${duration} ${timingFunction} ${delay} ${iterationCount} ${fillMode}`,
  };
};

export const useThemeTransition = (properties: string[] = ['all']) => {
  return {
    transition: properties.map(prop => `${prop} 0.3s ease-in-out`).join(', '),
  };
};

type HoverEffectConfig = {
  scale?: number;
  rotate?: number;
  translateY?: string;
  glow?: boolean;
};

export const useThemedHoverEffect = (config: HoverEffectConfig = {}) => {
  const { currentTheme } = useTheme();
  const {
    scale = 1.05,
    rotate = 0,
    translateY = '0',
    glow = true,
  } = config;

  return {
    transform: 'scale(1) rotate(0) translateY(0)',
    transition: 'all 0.3s ease-in-out',
    _hover: {
      transform: `scale(${scale}) rotate(${rotate}deg) translateY(${translateY})`,
      ...(glow && {
        boxShadow: `0 0 20px ${currentTheme.colors.card.glow}`,
      }),
    },
  };
};

type ButtonAnimationVariant = 'bounce' | 'glow' | 'shake' | 'rotate';

export const useThemedButtonAnimation = (variant: ButtonAnimationVariant = 'bounce') => {
  const { currentTheme } = useTheme();
  const prefersReducedMotion = usePrefersReducedMotion();

  if (prefersReducedMotion) {
    return {};
  }

  const animationMap: Record<ButtonAnimationVariant, string> = {
    bounce: animations.bounceScale,
    glow: animations.glowPulse,
    shake: animations.shakeAnimation,
    rotate: animations.rotateGlow,
  };

  return {
    animation: `${animationMap[variant]} 2s infinite`,
    '&:hover': {
      animation: 'none',
      transform: 'scale(1.05)',
      boxShadow: `0 0 15px ${currentTheme.colors.button.glow}`,
    },
  };
};