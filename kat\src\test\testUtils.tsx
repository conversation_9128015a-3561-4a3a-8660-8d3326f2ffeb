import React, { type ReactNode } from 'react';
import { Provider } from 'react-redux';
import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { render, type RenderOptions } from '@testing-library/react';
import { LoadingProvider } from '../hooks/useLoadingState';
import achievementReducer from '../store/slices/achievementSlice';
import type { RootState } from '../store';

type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

const rootReducer = combineReducers({
  achievement: achievementReducer,
  // Add other reducers as needed
});

type TestStoreState = ReturnType<typeof rootReducer>;

interface ExtendedRenderOptions extends Omit<RenderOptions, 'queries'> {
  preloadedState?: DeepPartial<TestStoreState>;
  store?: ReturnType<typeof createTestStore>;
}

export const createTestStore = (preloadedState?: DeepPartial<TestStoreState>) => {
  return configureStore({
    reducer: rootReducer,
    preloadedState: preloadedState as any,
  });
};

const defaultStore = createTestStore();

export function renderWithProviders(
  ui: React.ReactElement,
  {
    preloadedState,
    store = createTestStore(preloadedState),
    ...renderOptions
  }: ExtendedRenderOptions = {}
) {
  function Wrapper({ children }: { children: ReactNode }) {
    return (
      <Provider store={store}>
        <LoadingProvider>
          {children}
        </LoadingProvider>
      </Provider>
    );
  }

  return {
    store,
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
  };
}

export function createWrapper(store = defaultStore) {
  return function Wrapper({ children }: { children: ReactNode }) {
    return (
      <Provider store={store}>
        <LoadingProvider>
          {children}
        </LoadingProvider>
      </Provider>
    );
  };
}

// Helper for creating initial achievement state
export function createAchievementState(
  override?: DeepPartial<TestStoreState['achievement']>
): TestStoreState['achievement'] {
  return {
    achievements: [],
    userAchievements: [],
    lastUnlocked: null,
    ...override,
  } as TestStoreState['achievement'];
}

// Re-export everything from testing-library for convenience
export * from '@testing-library/react';