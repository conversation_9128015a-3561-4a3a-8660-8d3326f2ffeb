<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .card {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            text-align: center;
        }
        h1 { font-size: 2.5em; margin-bottom: 20px; }
        .status { font-size: 1.2em; margin: 20px 0; }
        .success { color: #4CAF50; font-weight: bold; }
        .info { background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="card">
        <h1>🎯 KAT Debug Test</h1>
        <div class="status success">✅ SERVER IS WORKING!</div>
        <div class="info">
            <p><strong>If you can see this page, the Vite server is running correctly.</strong></p>
            <p>URL: <code>http://localhost:4000/debug.html</code></p>
            <p>Time: <span id="time"></span></p>
        </div>
        <div class="info">
            <p><strong>Next step:</strong> The React app should work at <code>http://localhost:4000/</code></p>
        </div>
    </div>
    
    <script>
        document.getElementById('time').textContent = new Date().toLocaleString();
        
        // Test if we can make requests
        fetch('/debug.html')
            .then(() => console.log('✅ Fetch test successful'))
            .catch(err => console.error('❌ Fetch test failed:', err));
    </script>
</body>
</html>
