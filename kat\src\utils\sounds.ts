class SoundEffect {
  private audioContext: AudioContext | null = null;

  private createAudioContext() {
    if (!this.audioContext) {
      this.audioContext = new window.AudioContext();
    }
    return this.audioContext;
  }

  private async createSound(frequency: number, duration: number, type: OscillatorType = 'sine') {
    const context = this.createAudioContext();
    const oscillator = context.createOscillator();
    const gainNode = context.createGain();

    oscillator.type = type;
    oscillator.frequency.setValueAtTime(frequency, context.currentTime);
    
    // Envelope to avoid clicks
    gainNode.gain.setValueAtTime(0, context.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.3, context.currentTime + 0.01);
    gainNode.gain.linearRampToValueAtTime(0, context.currentTime + duration);

    oscillator.connect(gainNode);
    gainNode.connect(context.destination);
    
    oscillator.start(context.currentTime);
    oscillator.stop(context.currentTime + duration);
  }

  async playTaskComplete() {
    // Happy sound: rising arpeggio
    const startFreq = 400;
    for (let i = 0; i < 3; i++) {
      await this.createSound(startFreq * Math.pow(1.2, i), 0.15, 'triangle');
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  async playAchievement() {
    // Triumphant sound: fanfare-like
    await this.createSound(500, 0.1, 'triangle');
    await new Promise(resolve => setTimeout(resolve, 50));
    await this.createSound(600, 0.2, 'triangle');
    await new Promise(resolve => setTimeout(resolve, 50));
    await this.createSound(800, 0.3, 'triangle');
  }

  async playReward() {
    // Magical sound: sparkle effect
    for (let i = 0; i < 4; i++) {
      await this.createSound(1200 - i * 100, 0.1, 'sine');
      await new Promise(resolve => setTimeout(resolve, 50));
    }
  }

  async playLevelUp() {
    // Epic level up sound: ascending chord
    await this.createSound(300, 0.2, 'sine');
    await new Promise(resolve => setTimeout(resolve, 50));
    await this.createSound(400, 0.2, 'sine');
    await new Promise(resolve => setTimeout(resolve, 50));
    await this.createSound(500, 0.3, 'sine');
    await new Promise(resolve => setTimeout(resolve, 50));
    await this.createSound(600, 0.4, 'sine');
  }

  async playStreak() {
    // Energetic streak sound: quick ascending notes
    await this.createSound(400, 0.1, 'square');
    await new Promise(resolve => setTimeout(resolve, 30));
    await this.createSound(600, 0.15, 'square');
  }

  async playClick() {
    // Simple UI click sound
    await this.createSound(800, 0.05, 'sine');
  }
}

const soundEffect = new SoundEffect();

export const playSound = async (
  type: 'task' | 'achievement' | 'reward' | 'levelUp' | 'streak' | 'click'
) => {
  try {
    switch (type) {
      case 'task':
        await soundEffect.playTaskComplete();
        break;
      case 'achievement':
        await soundEffect.playAchievement();
        break;
      case 'reward':
        await soundEffect.playReward();
        break;
      case 'levelUp':
        await soundEffect.playLevelUp();
        break;
      case 'streak':
        await soundEffect.playStreak();
        break;
      case 'click':
        await soundEffect.playClick();
        break;
    }
  } catch (error) {
    console.warn('Sound playback failed:', error);
  }
};