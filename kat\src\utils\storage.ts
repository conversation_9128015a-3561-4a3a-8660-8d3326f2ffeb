const STORAGE_KEYS = {
  PROFILES: 'kat_profiles',
  SELECTED_PROFILE: 'kat_selected_profile',
  TASKS: 'kat_tasks',
  ACHIEVEMENTS: 'kat_achievements',
  USER_ACHIEVEMENTS: 'kat_user_achievements',
  PARENT_SETTINGS: 'kat_parent_settings',
  LANGUAGE: 'kat_language',
} as const;

const CRITICAL_STORAGE_KEYS = [
  STORAGE_KEYS.PROFILES,
  STORAGE_KEYS.SELECTED_PROFILE,
  STORAGE_KEYS.PARENT_SETTINGS,
] as const;

class StorageManager {
  static get<T>(key: string, defaultValue: T): T {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue
    } catch (error) {
      console.error(`Error reading from localStorage:`, error)
      this.handleStorageError(error);
      return defaultValue
    }
  }

  static set<T>(key: string, value: T): void {
    try {
      const serializedValue = JSON.stringify(value)
      localStorage.setItem(key, serializedValue)
    } catch (error) {
      console.error(`Error writing to localStorage:`, error)
      this.handleStorageError(error);

      // If it's a quota exceeded error, try to clear some space
      if (error instanceof Error && error.name === 'QuotaExceededError') {
        this.handleQuotaExceeded();
        // Retry the operation
        try {
          localStorage.setItem(key, JSON.stringify(value))
        } catch (retryError) {
          console.error('Failed to write even after cleanup:', retryError)
        }
      }
    }
  }

  static remove(key: string): void {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error(`Error removing from localStorage:`, error)
      this.handleStorageError(error);
    }
  }

  static clear(): void {
    try {
      localStorage.clear()
    } catch (error) {
      console.error(`Error clearing localStorage:`, error)
      this.handleStorageError(error);
    }
  }

  private static handleStorageError(error: unknown): void {
    // Check if localStorage is available
    if (!window.localStorage) {
      throw new Error('Local storage is not available. The app requires local storage to function properly.');
    }

    // Handle specific storage errors
    if (error instanceof Error) {
      const errorMessages = {
        QuotaExceededError: 'Storage space is full. The app will try to free up space by removing non-critical data.',
        SecurityError: 'Unable to access storage due to browser security settings. Please ensure storage access is enabled.',
        'NS_ERROR_DOM_QUOTA_REACHED': 'Storage space is full. The app will try to free up space by removing non-critical data.',
        default: 'A storage error occurred. Some data may not be saved properly.'
      };

      const message = errorMessages[error.name as keyof typeof errorMessages] || errorMessages.default;

      if (error.name === 'QuotaExceededError' || error.name === 'NS_ERROR_DOM_QUOTA_REACHED') {
        console.warn(message);
      } else {
        console.error(message, error);
      }

      // Display user-friendly toast if available
      if (typeof window !== 'undefined' && 'dispatchEvent' in window) {
        window.dispatchEvent(new CustomEvent('storage-error', {
          detail: { message, severity: error.name === 'QuotaExceededError' ? 'warning' : 'error' }
        }));
      }
    }
  }

  private static isCriticalData(key: string): boolean {
    return CRITICAL_STORAGE_KEYS.includes(key as typeof CRITICAL_STORAGE_KEYS[number]);
  }

  private static handleQuotaExceeded(): void {
    // Get all keys and their sizes
    const items = { ...localStorage };
    const itemSizes = Object.entries(items).map(([key, value]) => ({
      key,
      size: (value?.length || 0) * 2, // Approximate size in bytes
      isCritical: this.isCriticalData(key)
    }));

    // Sort by size, largest first, but put non-critical items first
    itemSizes.sort((a, b) => {
      if (a.isCritical !== b.isCritical) {
        return a.isCritical ? 1 : -1;
      }
      return b.size - a.size;
    });

    // Remove largest non-critical items until we've cleared about 20% of space
    const totalSize = itemSizes.reduce((sum, item) => sum + item.size, 0);
    const targetClear = totalSize * 0.2;
    let clearedSize = 0;
    let removedItems = 0;

    for (const item of itemSizes) {
      if (clearedSize >= targetClear || item.isCritical) break;

      try {
        localStorage.removeItem(item.key);
        clearedSize += item.size;
        removedItems++;
      } catch (error) {
        console.error(`Failed to remove item ${item.key}:`, error);
      }
    }

    // Display a user-friendly warning if storage is getting full
    if (clearedSize < targetClear) {
      console.warn('Storage space is critically low. Please consider exporting your data.');
    }
  }
}

export { STORAGE_KEYS, StorageManager }