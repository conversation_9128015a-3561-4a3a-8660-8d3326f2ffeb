import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON>,
  <PERSON>conButton,
  HStack,
  Text,
  Icon,
} from '@chakra-ui/react';
import { FiSun } from 'react-icons/fi';
import { FaRocket, FaIceCream, FaBolt } from 'react-icons/fa';
import { useTheme, useThemeStyles } from '../styles/themes/ThemeContext';
import type { ThemeVariant } from '../styles/themes/types';

interface ThemeOption {
  value: ThemeVariant;
  label: string;
  icon: React.ElementType;
  description: string;
}

const themeOptions: ThemeOption[] = [
  {
    value: 'space',
    label: 'Space Adventure',
    icon: FaRocket,
    description: 'Dark and cosmic theme',
  },
  {
    value: 'candy',
    label: 'Sweet Candy',
    icon: FaIceCream,
    description: 'Bright and playful theme',
  },
  {
    value: 'neon',
    label: 'Neon Future',
    icon: FaBolt,
    description: 'Vibrant cyberpunk theme',
  },
];

const ThemeSwitcher = () => {
  const { themeVariant, setThemeVariant, currentTheme } = useTheme();
  const { buttonStyle, cardStyle } = useThemeStyles();

  const selectedTheme = themeOptions.find(theme => theme.value === themeVariant);

  return (
    <Menu>
      <MenuButton
        as={IconButton}
        icon={
          <HStack spacing={2}>
            <Icon as={selectedTheme?.icon || FiSun} boxSize="1.4em" />
            <Text fontSize="sm" display={{ base: 'none', md: 'block' }}>
              Theme
            </Text>
          </HStack>
        }
        aria-label="Change theme"
        {...buttonStyle}
        size="lg"
        p={4}
        sx={{
          position: 'relative',
          '@keyframes rotate': {
            '0%': {
              transform: 'rotate(0deg)',
            },
            '100%': {
              transform: 'rotate(360deg)',
            }
          },
          '@keyframes pulse': {
            '0%': {
              opacity: 0.4,
              filter: 'blur(8px)',
            },
            '50%': {
              opacity: 0.7,
              filter: 'blur(12px)',
            },
            '100%': {
              opacity: 0.4,
              filter: 'blur(8px)',
            }
          },
          '&::before': {
            content: '""',
            position: 'absolute',
            top: '-3px',
            right: '-3px',
            bottom: '-3px',
            left: '-3px',
            borderRadius: 'inherit',
            background: `linear-gradient(45deg, ${currentTheme.colors.button.bg}, ${currentTheme.colors.achievement.unlocked}, ${currentTheme.colors.button.bg})`,
            backgroundSize: '200% 200%',
            zIndex: -2,
            animation: 'rotate 3s linear infinite',
          },
          '&::after': {
            content: '""',
            position: 'absolute',
            top: '-6px',
            right: '-6px',
            bottom: '-6px',
            left: '-6px',
            borderRadius: 'inherit',
            background: `linear-gradient(45deg, ${currentTheme.colors.achievement.unlocked}, ${currentTheme.colors.button.bg}, ${currentTheme.colors.achievement.unlocked})`,
            opacity: 0.6,
            filter: 'blur(8px)',
            zIndex: -1,
            animation: 'pulse 2s ease-in-out infinite',
          },
          '&:hover::after': {
            opacity: 0.8,
            filter: 'blur(12px)',
            animationDuration: '1.5s',
          },
        }}
      />
      <MenuList
        {...cardStyle}
        border="2px solid"
        borderColor={currentTheme.colors.card.border}
        p={2}
      >
        {themeOptions.map(option => (
          <MenuItem
            key={option.value}
            onClick={() => setThemeVariant(option.value)}
            icon={<Icon as={option.icon} />}
            bg="transparent"
            borderRadius="md"
            mb={1}
            _hover={{
              bg: currentTheme.colors.card.hover,
            }}
            _active={{
              bg: currentTheme.colors.card.hover,
            }}
            position="relative"
            sx={{
              '&::after': {
                content: '""',
                position: 'absolute',
                left: '-8px',
                top: '50%',
                transform: 'translateY(-50%)',
                width: '4px',
                height: '70%',
                borderRadius: 'full',
                bg: themeVariant === option.value ? currentTheme.colors.button.bg : 'transparent',
                transition: 'all 0.2s ease-in-out',
              },
            }}
          >
            <HStack spacing={3}>
              <Text
                color={currentTheme.colors.text.primary}
                fontWeight={themeVariant === option.value ? 'bold' : 'normal'}
              >
                {option.label}
              </Text>
              <Text
                fontSize="xs"
                color={currentTheme.colors.text.secondary}
                display={{ base: 'none', md: 'block' }}
              >
                {option.description}
              </Text>
            </HStack>
          </MenuItem>
        ))}
      </MenuList>
    </Menu>
  );
};

export default ThemeSwitcher;