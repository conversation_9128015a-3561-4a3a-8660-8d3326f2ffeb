import React from 'react'
import { createRoot } from 'react-dom/client'
// import App from './App'
import './index.css'

// Simple test component
const TestApp = () => {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>🎉 KAT App is Working!</h1>
      <p>The React application is loading successfully.</p>
      <p>Multilingual support has been implemented with:</p>
      <ul>
        <li>🇺🇸 English</li>
        <li>🇵🇱 Polish</li>
        <li>🇮🇸 Icelandic</li>
      </ul>
      <p>Time: {new Date().toLocaleString()}</p>
    </div>
  )
}

const container = document.getElementById('root')
if (!container) throw new Error('Failed to find the root element')

const root = createRoot(container)
root.render(
  <React.StrictMode>
    <TestApp />
  </React.StrictMode>
)