import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { <PERSON><PERSON>Provider } from '@chakra-ui/react';
import theme from '../../styles/theme';
import AchievementList from './AchievementList';
import achievementReducer from '../../store/slices/achievementSlice';
import taskReducer from '../../store/slices/taskSlice';
import profileReducer from '../../store/slices/profileSlice';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { Achievement, UserAchievement } from '../../types/achievement';
import { Profile } from '../../types/profile';
import { Task } from '../../types/task';
import type { RootState } from '../../store';

const mockAchievements: Achievement[] = [
  {
    id: '1',
    title: 'First Steps',
    description: 'Complete your first task',
    imageUrl: '🎯',
    condition: {
      type: 'TASKS_COMPLETED',
      value: 1
    }
  },
  {
    id: '2',
    title: 'Point Master',
    description: 'Earn 100 points',
    imageUrl: '⭐',
    condition: {
      type: 'POINTS_EARNED',
      value: 100
    }
  }
];

const mockUserAchievements: UserAchievement[] = [
  {
    profileId: 'test-profile',
    achievementId: '1',
    unlockedAt: new Date('2024-01-01').getTime()
  }
];

const mockProfiles: Profile[] = [
  {
    id: 'test-profile',
    name: 'Test User',
    avatarColor: 'blue.400',
    points: 50,
    createdAt: Date.now(),
    achievements: [],
    completedTasks: []
  }
];

const mockTasks: Task[] = [
  {
    id: '1',
    title: 'Test Task',
    reward: 10,
    assignedTo: ['test-profile'],
    completed: true,
    completedAt: Date.now(),
    createdAt: Date.now(),
    category: 'chores'
  }
];

interface TestState {
  achievement: {
    achievements: Achievement[];
    userAchievements: UserAchievement[];
    lastUnlocked: Achievement | null;
  };
  task: {
    tasks: Task[];
    isLoading: boolean;
    error: string | null;
  };
  profile: {
    profiles: Profile[];
    selectedProfileId: string | null;
    isLoading: boolean;
    error: string | null;
  };
}

const initialState: TestState = {
  achievement: {
    achievements: mockAchievements,
    userAchievements: mockUserAchievements,
    lastUnlocked: null
  },
  task: {
    tasks: mockTasks,
    isLoading: false,
    error: null
  },
  profile: {
    profiles: mockProfiles,
    selectedProfileId: 'test-profile',
    isLoading: false,
    error: null
  }
};

const createTestStore = () => {
  return configureStore({
    reducer: {
      achievement: achievementReducer,
      task: taskReducer,
      profile: profileReducer
    },
    preloadedState: initialState
  });
};

const renderWithProviders = (ui: React.ReactElement) => {
  return render(
    <ChakraProvider theme={theme}>
      <Provider store={createTestStore()}>
        {ui}
      </Provider>
    </ChakraProvider>
  );
};

describe('AchievementList', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders achievement list with correct titles', () => {
    renderWithProviders(<AchievementList />);

    expect(screen.getByText('First Steps')).toBeInTheDocument();
    expect(screen.getByText('Point Master')).toBeInTheDocument();
  });

  it('shows unlocked status for completed achievements', () => {
    renderWithProviders(<AchievementList />);

    expect(screen.getByText(/Unlocked/)).toBeInTheDocument();
    // Find the element containing the date using a regex pattern to handle whitespace
    const datePattern = /1\.01\.2024/;
    const dateElement = screen.getByText(datePattern);
    expect(dateElement).toBeInTheDocument();
  });

  it('shows progress for incomplete achievements', () => {
    renderWithProviders(<AchievementList />);

    expect(screen.getByText('50/100')).toBeInTheDocument();
  });

  it('displays achievement descriptions', () => {
    renderWithProviders(<AchievementList />);

    expect(screen.getByText('Complete your first task')).toBeInTheDocument();
    // Use queryAllByText for text that appears multiple times
    const pointsAchievementTexts = screen.queryAllByText('Earn 100 points');
    expect(pointsAchievementTexts.length).toBeGreaterThan(0);
  });
});