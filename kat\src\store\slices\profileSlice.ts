import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { Profile, CreateProfileData, UpdateProfileData } from '../../types/profile';
import { StorageManager } from '../../utils/storage';
import { RootState } from '..';

interface ProfileState {
  profiles: Profile[];
  selectedProfileId: string | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: ProfileState = {
  profiles: StorageManager.get('profiles', []),
  selectedProfileId: StorageManager.get('selectedProfileId', null),
  isLoading: false,
  error: null,
};

const profileSlice = createSlice({
  name: 'profile',
  initialState,
  reducers: {
    createProfile: (state, action: PayloadAction<CreateProfileData>) => {
      const newProfile: Profile = {
        id: Date.now().toString(),
        name: action.payload.name,
        avatarColor: action.payload.avatarColor,
        avatarUrl: action.payload.avatarUrl,
        createdAt: Date.now(),
        points: 0,
        achievements: [],
        completedTasks: [],
      };
      state.profiles.push(newProfile);
      StorageManager.set('profiles', state.profiles);
    },
    selectProfile: (state, action: PayloadAction<string>) => {
      state.selectedProfileId = action.payload;
      StorageManager.set('selectedProfileId', action.payload);
    },
    updateProfile: (state, action: PayloadAction<{ id: string; data: UpdateProfileData }>) => {
      const { id, data } = action.payload;
      const profile = state.profiles.find(p => p.id === id);
      if (profile) {
        Object.assign(profile, data);
        StorageManager.set('profiles', state.profiles);
      }
    },
    addPoints: (state, action: PayloadAction<{ profileId: string; points: number }>) => {
      const profile = state.profiles.find(p => p.id === action.payload.profileId);
      if (profile) {
        profile.points += action.payload.points;
        StorageManager.set('profiles', state.profiles);
      }
    },
    updateAvatar: (state, action: PayloadAction<{ profileId: string; avatarUrl: string }>) => {
      const profile = state.profiles.find(p => p.id === action.payload.profileId);
      if (profile) {
        profile.avatarUrl = action.payload.avatarUrl;
        StorageManager.set('profiles', state.profiles);
      }
    },
    deleteProfile: (state, action: PayloadAction<string>) => {
      state.profiles = state.profiles.filter(p => p.id !== action.payload);
      if (state.selectedProfileId === action.payload) {
        state.selectedProfileId = null;
      }
      StorageManager.set('profiles', state.profiles);
      StorageManager.set('selectedProfileId', state.selectedProfileId);
    }
  },
});

export const selectProfiles = (state: RootState) => state.profile.profiles;
export const selectSelectedProfile = (state: RootState) => 
  state.profile.profiles.find(p => p.id === state.profile.selectedProfileId);
export const selectSelectedProfileId = (state: RootState) => state.profile.selectedProfileId;

export const {
  createProfile,
  selectProfile,
  updateProfile,
  addPoints,
  updateAvatar,
  deleteProfile
} = profileSlice.actions;

export default profileSlice.reducer;