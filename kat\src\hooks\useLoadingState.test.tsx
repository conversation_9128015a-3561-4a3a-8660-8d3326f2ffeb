import { describe, test, expect, vi } from 'vitest';
import { render, screen, act } from '@testing-library/react';
import { LoadingProvider, useLoadingState } from './useLoadingState';
import React from 'react';

// Test component that uses the loading state
const TestComponent = () => {
  const { isLoading, message, setLoading } = useLoadingState();

  return (
    <div>
      <div data-testid="loading-status">
        {isLoading ? 'Loading' : 'Not Loading'}
      </div>
      {message && <div data-testid="loading-message">{message}</div>}
      <button
        onClick={() => setLoading(true, 'Test Loading')}
        data-testid="start-loading"
      >
        Start Loading
      </button>
      <button
        onClick={() => setLoading(false)}
        data-testid="stop-loading"
      >
        Stop Loading
      </button>
    </div>
  );
};

describe('useLoadingState', () => {
  test('should provide loading state and methods', () => {
    render(
      <LoadingProvider>
        <TestComponent />
      </LoadingProvider>
    );

    // Initially not loading
    expect(screen.getByTestId('loading-status')).toHaveTextContent('Not Loading');
    expect(screen.queryByTestId('loading-message')).not.toBeInTheDocument();

    // Start loading
    act(() => {
      screen.getByTestId('start-loading').click();
    });

    expect(screen.getByTestId('loading-status')).toHaveTextContent('Loading');
    expect(screen.getByTestId('loading-message')).toHaveTextContent('Test Loading');

    // Stop loading
    act(() => {
      screen.getByTestId('stop-loading').click();
    });

    expect(screen.getByTestId('loading-status')).toHaveTextContent('Not Loading');
    expect(screen.queryByTestId('loading-message')).not.toBeInTheDocument();
  });

  test('should throw error if used outside provider', () => {
    // Suppress console.error for this test
    const consoleSpy = vi.spyOn(console, 'error');
    consoleSpy.mockImplementation(() => {});

    expect(() => {
      render(<TestComponent />);
    }).toThrow('useLoadingState must be used within a LoadingProvider');

    consoleSpy.mockRestore();
  });

  test('should handle multiple loading states', async () => {
    const { getByTestId } = render(
      <LoadingProvider>
        <TestComponent />
      </LoadingProvider>
    );

    // Start loading with message
    act(() => {
      getByTestId('start-loading').click();
    });

    expect(getByTestId('loading-status')).toHaveTextContent('Loading');
    expect(getByTestId('loading-message')).toHaveTextContent('Test Loading');

    // Update loading state without message
    act(() => {
      getByTestId('start-loading').click();
    });

    expect(getByTestId('loading-status')).toHaveTextContent('Loading');

    // Stop loading
    act(() => {
      getByTestId('stop-loading').click();
    });

    expect(getByTestId('loading-status')).toHaveTextContent('Not Loading');
  });
});