// Level thresholds - each index represents level - 1
// e.g., level 1 needs 0 points, level 2 needs 100 points, etc.
export const LEVEL_THRESHOLDS = [0, 100, 250, 500, 1000, 2000, 4000, 8000, 16000, 32000];

export const calculateLevel = (points: number): { level: number; progress: number } => {
  let level = 1;
  for (let i = 1; i < LEVEL_THRESHOLDS.length; i++) {
    if (points >= LEVEL_THRESHOLDS[i]) {
      level = i + 1;
    } else {
      break;
    }
  }

  // Calculate progress to next level
  const currentThreshold = LEVEL_THRESHOLDS[level - 1];
  const nextThreshold = LEVEL_THRESHOLDS[level] || currentThreshold * 2;
  const progress = ((points - currentThreshold) / (nextThreshold - currentThreshold)) * 100;

  return { level, progress };
};