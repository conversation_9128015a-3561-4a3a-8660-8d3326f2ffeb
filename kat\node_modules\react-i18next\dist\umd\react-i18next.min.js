!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],n):n((e="undefined"!=typeof globalThis?globalThis:e||self).ReactI18next={},e.React)}(this,(function(e,n){"use strict";function t(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var s=t({area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}),r=/\s([^'"/\s><]+?)[\s/>]|([^\s=]+)=\s?(".*?"|'.*?')/g;function i(e){var n={type:"tag",name:"",voidElement:!1,attrs:{},children:[]},t=e.match(/<\/?([^\s]+?)[/\s>]/);if(t&&(n.name=t[1],(s[t[1]]||"/"===e.charAt(e.length-2))&&(n.voidElement=!0),n.name.startsWith("!--"))){var i=e.indexOf("--\x3e");return{type:"comment",comment:-1!==i?e.slice(4,i):""}}for(var a=new RegExp(r),o=null;null!==(o=a.exec(e));)if(o[0].trim())if(o[1]){var l=o[1].trim(),c=[l,""];l.indexOf("=")>-1&&(c=l.split("=")),n.attrs[c[0]]=c[1],a.lastIndex--}else o[2]&&(n.attrs[o[2]]=o[3].trim().substring(1,o[3].length-1));return n}var a=/<[a-zA-Z0-9\-\!\/](?:"[^"]*"|'[^']*'|[^'">])*>/g,o=/^\s*$/,l=Object.create(null);var c=function(e,n){n||(n={}),n.components||(n.components=l);var t,s=[],r=[],c=-1,u=!1;if(0!==e.indexOf("<")){var p=e.indexOf("<");s.push({type:"text",content:-1===p?e:e.substring(0,p)})}return e.replace(a,(function(a,l){if(u){if(a!=="</"+t.name+">")return;u=!1}var p,d="/"!==a.charAt(1),f=a.startsWith("\x3c!--"),g=l+a.length,m=e.charAt(g);if(f){var h=i(a);return c<0?(s.push(h),s):((p=r[c]).children.push(h),s)}if(d&&(c++,"tag"===(t=i(a)).type&&n.components[t.name]&&(t.type="component",u=!0),t.voidElement||u||!m||"<"===m||t.children.push({type:"text",content:e.slice(g,e.indexOf("<",g))}),0===c&&s.push(t),(p=r[c-1])&&p.children.push(t),r[c]=t),(!d||t.voidElement)&&(c>-1&&(t.voidElement||t.name===a.slice(2,-1))&&(c--,t=-1===c?s:r[c]),!u&&"<"!==m&&m)){p=-1===c?s:r[c].children;var y=e.indexOf("<",g),x=e.slice(g,-1===y?void 0:y);o.test(x)&&(x=" "),(y>-1&&c+p.length>=0||" "!==x)&&p.push({type:"text",content:x})}})),s};const u=(e,n,t,s)=>{const r=[t,{code:n,...s||{}}];if(e?.services?.logger?.forward)return e.services.logger.forward(r,"warn","react-i18next::",!0);y(r[0])&&(r[0]=`react-i18next:: ${r[0]}`),e?.services?.logger?.warn?e.services.logger.warn(...r):console?.warn&&console.warn(...r)},p={},d=(e,n,t,s)=>{y(t)&&p[t]||(y(t)&&(p[t]=new Date),u(e,n,t,s))},f=(e,n)=>()=>{if(e.isInitialized)n();else{const t=()=>{setTimeout((()=>{e.off("initialized",t)}),0),n()};e.on("initialized",t)}},g=(e,n,t)=>{e.loadNamespaces(n,f(e,t))},m=(e,n,t,s)=>{if(y(t)&&(t=[t]),e.options.preload&&e.options.preload.indexOf(n)>-1)return g(e,t,s);t.forEach((n=>{e.options.ns.indexOf(n)<0&&e.options.ns.push(n)})),e.loadLanguages(n,f(e,s))},h=e=>e.displayName||e.name||(y(e)&&e.length>0?e:"Unknown"),y=e=>"string"==typeof e,x=e=>"object"==typeof e&&null!==e,v=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,N={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},b=e=>N[e];let E={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(v,b)};const O=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};E={...E,...e}},I=()=>E;let S;const w=e=>{S=e},$=()=>S,T=(e,n)=>{if(!e)return!1;const t=e.props?.children??e.children;return n?t.length>0:!!t},k=e=>{if(!e)return[];const n=e.props?.children??e.children;return e.props?.i18nIsDynamicList?R(n):n},R=e=>Array.isArray(e)?e:[e],A=(e,t,s,r)=>{if(!e)return"";let i="";const a=R(e),o=t?.transSupportBasicHtmlNodes?t.transKeepBasicHtmlNodesFor??[]:[];return a.forEach(((e,a)=>{if(y(e))i+=`${e}`;else if(n.isValidElement(e)){const{props:n,type:l}=e,c=Object.keys(n).length,u=o.indexOf(l)>-1,p=n.children;if(!p&&u&&!c)return void(i+=`<${l}/>`);if(!p&&(!u||c)||n.i18nIsDynamicList)return void(i+=`<${a}></${a}>`);if(u&&1===c&&y(p))return void(i+=`<${l}>${p}</${l}>`);const d=A(p,t,s,r);i+=`<${a}>${d}</${a}>`}else if(null!==e)if(x(e)){const{format:n,...t}=e,a=Object.keys(t);if(1===a.length){const e=n?`${a[0]}, ${n}`:a[0];return void(i+=`{{${e}}}`)}u(s,"TRANS_INVALID_OBJ","Invalid child - Object should only have keys {{ value, format }} (format is optional).",{i18nKey:r,child:e})}else u(s,"TRANS_INVALID_VAR","Passed in a variable like {number} - pass variables for interpolation as full objects like {{number}}.",{i18nKey:r,child:e});else u(s,"TRANS_NULL_VALUE","Passed in a null value as child",{i18nKey:r})})),i},j=(e,t,s,r,i,a)=>{if(""===t)return[];const o=r.transKeepBasicHtmlNodesFor||[],l=t&&new RegExp(o.map((e=>`<${e}`)).join("|")).test(t);if(!e&&!l&&!a)return[t];const u={},p=e=>{R(e).forEach((e=>{y(e)||(T(e)?p(k(e)):x(e)&&!n.isValidElement(e)&&Object.assign(u,e))}))};p(e);const d=c(`<0>${t}</0>`),f={...u,...i},g=(e,t,s)=>{const r=k(e),i=h(r,t.children,s);return(e=>Array.isArray(e)&&e.every(n.isValidElement))(r)&&0===i.length||e.props?.i18nIsDynamicList?r:i},m=(e,t,s,r,i)=>{e.dummy?(e.children=t,s.push(n.cloneElement(e,{key:r},i?void 0:t))):s.push(...n.Children.map([e],(e=>{const s={...e.props};return delete s.i18nIsDynamicList,n.createElement(e.type,{...s,key:r,ref:e.props.ref??e.ref},i?null:t)})))},h=(t,i,c)=>{const u=R(t);return R(i).reduce(((t,i,p)=>{const d=i.children?.[0]?.content&&s.services.interpolator.interpolate(i.children[0].content,f,s.language);if("tag"===i.type){let a=u[parseInt(i.name,10)];1!==c.length||a||(a=c[0][i.name]),a||(a={});const v=0!==Object.keys(i.attrs).length?((e,n)=>{const t={...n};return t.props=Object.assign(e.props,n.props),t})({props:i.attrs},a):a,N=n.isValidElement(v),b=N&&T(i,!0)&&!i.voidElement,E=l&&x(v)&&v.dummy&&!N,O=x(e)&&Object.hasOwnProperty.call(e,i.name);if(y(v)){const e=s.services.interpolator.interpolate(v,f,s.language);t.push(e)}else if(T(v)||b){const e=g(v,i,c);m(v,e,t,p)}else if(E){const e=h(u,i.children,c);m(v,e,t,p)}else if(Number.isNaN(parseFloat(i.name)))if(O){const e=g(v,i,c);m(v,e,t,p,i.voidElement)}else if(r.transSupportBasicHtmlNodes&&o.indexOf(i.name)>-1)if(i.voidElement)t.push(n.createElement(i.name,{key:`${i.name}-${p}`}));else{const e=h(u,i.children,c);t.push(n.createElement(i.name,{key:`${i.name}-${p}`},e))}else if(i.voidElement)t.push(`<${i.name} />`);else{const e=h(u,i.children,c);t.push(`<${i.name}>${e}</${i.name}>`)}else if(x(v)&&!N){const e=i.children[0]?d:null;e&&t.push(e)}else m(v,d,t,p,1!==i.children.length||!d)}else if("text"===i.type){const e=r.transWrapTextNodes,o=a?r.unescape(s.services.interpolator.interpolate(i.content,f,s.language)):s.services.interpolator.interpolate(i.content,f,s.language);e?t.push(n.createElement(e,{key:`${i.name}-${p}`},o)):t.push(o)}return t}),[])},v=h([{dummy:!0,children:e||[]}],d,R(e||[]));return k(v[0])},C=(e,t,s)=>{const r=e.key||t,i=n.cloneElement(e,{key:r});if(!i.props||!i.props.children||s.indexOf(`${t}/>`)<0&&s.indexOf(`${t} />`)<0)return i;return n.createElement((function(){return n.createElement(n.Fragment,null,i)}),{key:r})},L=(e,n,t,s)=>e?Array.isArray(e)?((e,n)=>e.map(((e,t)=>C(e,t,n))))(e,n):x(e)?((e,n)=>{const t={};return Object.keys(e).forEach((s=>{Object.assign(t,{[s]:C(e[s],s,n)})})),t})(e,n):(d(t,"TRANS_INVALID_COMPONENTS",'<Trans /> "components" prop expects an object or array',{i18nKey:s}),null):null;function P(e){let{children:t,count:s,parent:r,i18nKey:i,context:a,tOptions:o={},values:l,defaults:c,components:u,ns:p,i18n:f,t:g,shouldUnescape:m,...h}=e;const x=f||$();if(!x)return d(x,"NO_I18NEXT_INSTANCE","Trans: You need to pass in an i18next instance using i18nextReactModule",{i18nKey:i}),t;const v=g||x.t.bind(x)||(e=>e),N={...I(),...x.options?.react};let b=p||v.ns||x.options?.defaultNS;b=y(b)?[b]:b||["translation"];const E=A(t,N,x,i),O=c||E||N.transEmptyNodeValue||i,{hashTransKey:S}=N,w=i||(S?S(E||O):E||O);x.options?.interpolation?.defaultVariables&&(l=l&&Object.keys(l).length>0?{...l,...x.options.interpolation.defaultVariables}:{...x.options.interpolation.defaultVariables});const T=l||void 0!==s&&!x.options?.interpolation?.alwaysFormat||!t?o.interpolation:{interpolation:{...o.interpolation,prefix:"#$?",suffix:"?$#"}},k={...o,context:a||o.context,count:s,...l,...T,defaultValue:O,ns:b},R=w?v(w,k):O,C=L(u,R,x,i),P=j(C||t,R,x,N,k,m),V=r??N.defaultTransParent;return V?n.createElement(V,h,P):P}const V={type:"3rdParty",init(e){O(e.options.react),w(e)}},_=n.createContext();class D{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach((e=>{this.usedNamespaces[e]||(this.usedNamespaces[e]=!0)}))}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const K=e=>async n=>({...await(e.getInitialProps?.(n))??{},...z()}),z=()=>{const e=$(),n=e.reportNamespaces?.getUsedNamespaces()??[],t={},s={};return e.languages.forEach((t=>{s[t]={},n.forEach((n=>{s[t][n]=e.getResourceBundle(t,n)||{}}))})),t.initialI18nStore=s,t.initialLanguage=e.language,t};const U=(e,n,t,s)=>e.getFixedT(n,t,s),F=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{i18n:s}=t,{i18n:r,defaultNS:i}=n.useContext(_)||{},a=s||r||$();if(a&&!a.reportNamespaces&&(a.reportNamespaces=new D),!a){d(a,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const e=(e,n)=>y(n)?n:x(n)&&y(n.defaultValue)?n.defaultValue:Array.isArray(e)?e[e.length-1]:e,n=[e,{},!1];return n.t=e,n.i18n={},n.ready=!1,n}a.options.react?.wait&&d(a,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const o={...I(),...a.options.react,...t},{useSuspense:l,keyPrefix:c}=o;let u=e||i||a.options?.defaultNS;u=y(u)?[u]:u||["translation"],a.reportNamespaces.addUsedNamespaces?.(u);const p=(a.isInitialized||a.initializedStoreOnce)&&u.every((e=>function(e,n){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return n.languages&&n.languages.length?n.hasLoadedNamespace(e,{lng:t.lng,precheck:(n,s)=>{if(t.bindI18n?.indexOf("languageChanging")>-1&&n.services.backendConnector.backend&&n.isLanguageChangingTo&&!s(n.isLanguageChangingTo,e))return!1}}):(d(n,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:n.languages}),!0)}(e,a,o))),f=((e,t,s,r)=>n.useCallback(U(e,t,s,r),[e,t,s,r]))(a,t.lng||null,"fallback"===o.nsMode?u:u[0],c),h=()=>f,v=()=>U(a,t.lng||null,"fallback"===o.nsMode?u:u[0],c),[N,b]=n.useState(h);let E=u.join();t.lng&&(E=`${t.lng}${E}`);const O=((e,t)=>{const s=n.useRef();return n.useEffect((()=>{s.current=e}),[e,t]),s.current})(E),S=n.useRef(!0);n.useEffect((()=>{const{bindI18n:e,bindI18nStore:n}=o;S.current=!0,p||l||(t.lng?m(a,t.lng,u,(()=>{S.current&&b(v)})):g(a,u,(()=>{S.current&&b(v)}))),p&&O&&O!==E&&S.current&&b(v);const s=()=>{S.current&&b(v)};return e&&a?.on(e,s),n&&a?.store.on(n,s),()=>{S.current=!1,a&&e?.split(" ").forEach((e=>a.off(e,s))),n&&a&&n.split(" ").forEach((e=>a.store.off(e,s)))}}),[a,E]),n.useEffect((()=>{S.current&&p&&b(h)}),[a,c,p]);const w=[N,a,p];if(w.t=N,w.i18n=a,w.ready=p,p)return w;if(!p&&!l)return w;throw new Promise((e=>{t.lng?m(a,t.lng,u,(()=>e())):g(a,u,(()=>e()))}))};const B=function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const{i18n:r}=s,{i18n:i}=n.useContext(_)||{},a=r||i||$();a.options?.isClone||(e&&!a.initializedStoreOnce&&(a.services.resourceStore.data=e,a.options.ns=Object.values(e).reduce(((e,n)=>(Object.keys(n).forEach((n=>{e.indexOf(n)<0&&e.push(n)})),e)),a.options.ns),a.initializedStoreOnce=!0,a.isInitialized=!0),t&&!a.initializedLanguageOnce&&(a.changeLanguage(t),a.initializedLanguageOnce=!0))};e.I18nContext=_,e.I18nextProvider=function(e){let{i18n:t,defaultNS:s,children:r}=e;const i=n.useMemo((()=>({i18n:t,defaultNS:s})),[t,s]);return n.createElement(_.Provider,{value:i},r)},e.Trans=function(e){let{children:t,count:s,parent:r,i18nKey:i,context:a,tOptions:o={},values:l,defaults:c,components:u,ns:p,i18n:d,t:f,shouldUnescape:g,...m}=e;const{i18n:h,defaultNS:y}=n.useContext(_)||{},x=d||h||$(),v=f||x?.t.bind(x);return P({children:t,count:s,parent:r,i18nKey:i,context:a,tOptions:o,values:l,defaults:c,components:u,ns:p||v?.ns||y||x?.options?.defaultNS,i18n:x,t:f,shouldUnescape:g,...m})},e.TransWithoutContext=P,e.Translation=e=>{let{ns:n,children:t,...s}=e;const[r,i,a]=F(n,s);return t(r,{i18n:i,lng:i.language},a)},e.composeInitialProps=K,e.date=()=>"",e.getDefaults=I,e.getI18n=$,e.getInitialProps=z,e.initReactI18next=V,e.number=()=>"",e.plural=()=>"",e.select=()=>"",e.selectOrdinal=()=>"",e.setDefaults=O,e.setI18n=w,e.time=()=>"",e.useSSR=B,e.useTranslation=F,e.withSSR=()=>function(e){function t(t){let{initialI18nStore:s,initialLanguage:r,...i}=t;return B(s,r),n.createElement(e,{...i})}return t.getInitialProps=K(e),t.displayName=`withI18nextSSR(${h(e)})`,t.WrappedComponent=e,t},e.withTranslation=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return function(s){function r(r){let{forwardedRef:i,...a}=r;const[o,l,c]=F(e,{...a,keyPrefix:t.keyPrefix}),u={...a,t:o,i18n:l,tReady:c};return t.withRef&&i?u.ref=i:!t.withRef&&i&&(u.forwardedRef=i),n.createElement(s,u)}r.displayName=`withI18nextTranslation(${h(s)})`,r.WrappedComponent=s;return t.withRef?n.forwardRef(((e,t)=>n.createElement(r,Object.assign({},e,{forwardedRef:t})))):r}}}));
