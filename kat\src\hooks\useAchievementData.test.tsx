import { describe, test, expect, vi, beforeEach } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { useAchievementData } from './useAchievementData';
import { createWrapper, createTestStore } from '../test/testUtils';
import { createAchievement } from '../types/achievement';
import { ACHIEVEMENTS } from '../features/achievements/predefinedAchievements';

const mockAchievements = ACHIEVEMENTS.map(createAchievement);

const mockUserAchievements = [
  {
    profileId: 'test-profile',
    achievementId: mockAchievements[0].id,
    unlockedAt: Date.now(),
  },
];

describe('useAchievementData', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('should return initial achievement data', async () => {
    const store = createTestStore({
      achievement: {
        achievements: mockAchievements,
        userAchievements: [],
        lastUnlocked: null,
      },
    });

    const { result } = renderHook(
      () => useAchievementData('test-profile'),
      { wrapper: createWrapper(store) }
    );

    expect(result.current.achievements).toEqual(mockAchievements);
    expect(result.current.userAchievements).toEqual([]);
    expect(result.current.achievementStats).toEqual({
      total: mockAchievements.length,
      unlocked: 0,
      percentComplete: 0,
    });
  });

  test('should calculate achievement stats correctly', async () => {
    const store = createTestStore({
      achievement: {
        achievements: mockAchievements,
        userAchievements: mockUserAchievements,
        lastUnlocked: null,
      },
    });

    const { result } = renderHook(
      () => useAchievementData('test-profile'),
      { wrapper: createWrapper(store) }
    );

    await waitFor(() => {
      expect(result.current.achievementStats).toEqual({
        total: mockAchievements.length,
        unlocked: 1,
        percentComplete: Math.round((1 / mockAchievements.length) * 100),
      });
    });
  });

  test('should filter achievements by profile', () => {
    const otherProfileAchievement = {
      profileId: 'other-profile',
      achievementId: mockAchievements[1].id,
      unlockedAt: Date.now(),
    };

    const store = createTestStore({
      achievement: {
        achievements: mockAchievements,
        userAchievements: [...mockUserAchievements, otherProfileAchievement],
        lastUnlocked: null,
      },
    });

    const { result } = renderHook(
      () => useAchievementData('test-profile'),
      { wrapper: createWrapper(store) }
    );

    expect(result.current.userAchievements).toEqual(mockUserAchievements);
  });

  test('should handle loading state', async () => {
    const { result } = renderHook(
      () => useAchievementData('test-profile'),
      { wrapper: createWrapper() }
    );

    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  test('should handle empty profile id', () => {
    const { result } = renderHook(
      () => useAchievementData(''),
      { wrapper: createWrapper() }
    );

    expect(result.current.achievementStats).toEqual({
      total: 0,
      unlocked: 0,
      percentComplete: 0,
    });
    expect(result.current.userAchievements).toEqual([]);
  });
});