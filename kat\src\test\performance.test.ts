import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import { cache } from '../utils/cache';
import { store } from '../store';
import { createTask, completeTask } from '../store/slices/taskSlice';
import { unlockAchievement } from '../store/slices/achievementSlice';

describe('Performance Benchmarks', () => {
  beforeEach(() => {
    vi.useFakeTimers();
    performance.mark('test-start');
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  test('cache operations should complete within performance budget', async () => {
    const start = performance.now();
    
    // Test write performance
    for (let i = 0; i < 1000; i++) {
      await cache.set(`key-${i}`, { data: `value-${i}` });
    }
    
    // Test read performance
    for (let i = 0; i < 1000; i++) {
      await cache.get(`key-${i}`);
    }
    
    const end = performance.now();
    const duration = end - start;
    
    expect(duration).toBeLessThan(2000); // Should complete in less than 2 seconds
  });

  test('redux state updates should be efficient', () => {
    const start = performance.now();
    
    // Create 100 tasks
    for (let i = 0; i < 100; i++) {
      store.dispatch(createTask({
        title: `Task ${i}`,
        description: `Description ${i}`,
        reward: 10,
        assignedTo: ['test-user'],
        category: 'chores'
      }));
    }
    
    const mid = performance.now();
    const createTime = mid - start;
    expect(createTime).toBeLessThan(100); // Should take less than 100ms
    
    // Complete all tasks
    store.getState().task.tasks.forEach(task => {
      store.dispatch(completeTask(task.id));
    });
    
    const end = performance.now();
    const completeTime = end - mid;
    expect(completeTime).toBeLessThan(100); // Should take less than 100ms
  });

  test('achievement unlocking should be performant', () => {
    const start = performance.now();
    
    // Simulate unlocking multiple achievements
    for (let i = 0; i < 10; i++) {
      store.dispatch(unlockAchievement({
        profileId: 'test-user',
        achievementId: `achievement-${i}`
      }));
    }
    
    const end = performance.now();
    expect(end - start).toBeLessThan(50); // Should take less than 50ms
  });

  test('component render times are within budget', async () => {
    const renderTimings: number[] = [];
    
    // Mock requestAnimationFrame for consistent timing
    const requestAnimationFrameMock = vi.fn(cb => setTimeout(cb, 16));
    vi.stubGlobal('requestAnimationFrame', requestAnimationFrameMock);
    
    // Create performance observer
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      renderTimings.push(...entries.map(entry => entry.duration));
    });
    
    observer.observe({ entryTypes: ['measure'] });
    
    // Component render measurements happen in the component lifecycle
    performance.measure('component-render', 'test-start');
    
    // Advance timers to process renders
    await vi.runAllTimersAsync();
    
    // Cleanup
    observer.disconnect();
    
    // Assert render times
    renderTimings.forEach(time => {
      expect(time).toBeLessThan(16); // Should render within one frame (16ms)
    });
  });

  test('IndexedDB operations are optimized', async () => {
    const measurements: number[] = [];
    let totalSize = 0;
    
    // Write benchmark
    const startWrite = performance.now();
    for (let i = 0; i < 100; i++) {
      const data = { id: i, data: 'x'.repeat(1000) }; // 1KB of data
      await cache.set(`perf-test-${i}`, data);
      totalSize += JSON.stringify(data).length;
    }
    measurements.push(performance.now() - startWrite);
    
    // Read benchmark
    const startRead = performance.now();
    for (let i = 0; i < 100; i++) {
      await cache.get(`perf-test-${i}`);
    }
    measurements.push(performance.now() - startRead);
    
    // Cleanup
    await cache.clear();
    
    // Assertions
    measurements.forEach(time => {
      const operationsPerSecond = 100 / (time / 1000);
      expect(operationsPerSecond).toBeGreaterThan(100); // At least 100 ops/second
    });
    
    // Log performance metrics
    console.log(`Total data size: ${totalSize / 1024}KB`);
    console.log(`Write speed: ${measurements[0]}ms`);
    console.log(`Read speed: ${measurements[1]}ms`);
  });
});