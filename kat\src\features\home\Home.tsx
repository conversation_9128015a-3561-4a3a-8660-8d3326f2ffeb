import {
  <PERSON>,
  But<PERSON>,
  Heading,
  SimpleGrid,
  Text,
  VStack,
  HStack,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import PointsDisplay from '../../components/PointsDisplay';
import ProgressStats from '../../components/ProgressStats';
import { AddIcon, StarIcon } from '@chakra-ui/icons';
import StreakTracker from '../../components/StreakTracker';
import { useStreak } from '../../hooks/useStreak';
import TaskList from '../tasks/TaskList';
import TaskForm from '../tasks/TaskForm';
import AchievementList from '../achievements/AchievementList';
import ProfileSelection from '../profiles/ProfileSelection';
import { useDispatch, useSelector } from 'react-redux';
import { useState } from 'react';
import { RootState } from '../../store';
import { getPredefinedTasks } from '../tasks/predefinedTasks';
import { createTask } from '../../store/slices/taskSlice';
import { selectProfile } from '../../store/slices/profileSlice';
import { useNavigate } from 'react-router-dom';
import { useParentControls } from '../../hooks/useParentControls';
import PinProtection from '../parent/PinProtection';
import { usePoints } from '../../hooks/usePoints';
import LevelUpNotification from '../../components/LevelUpNotification';

const Home = () => {
  const { t } = useTranslation();
  const { isOpen: isTaskFormOpen, onOpen: onTaskFormOpen, onClose: onTaskFormClose } = useDisclosure();
  const { isOpen: isPinDialogOpen, onOpen: onPinDialogOpen, onClose: onPinDialogClose } = useDisclosure();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const toast = useToast();
  const [currentSection, setCurrentSection] = useState<'home' | 'tasks' | 'achievements' | 'rewards'>('home');
  const { canCreateTasks, canRedeemRewards } = useParentControls();
  const selectedProfileId = useSelector((state: RootState) => state.profile.selectedProfileId);
  const tasks = useSelector((state: RootState) => state.task.tasks);
  const {
    points,
    showLevelUp,
    newLevel,
    closeLevelUpNotification,
  } = usePoints(selectedProfileId || '');
  const streakProps = useStreak(selectedProfileId || '');

  const profileTasks = tasks.filter(task =>
    selectedProfileId && task.assignedTo.includes(selectedProfileId)
  );

  const handleAddPredefinedTasks = () => {
    if (!selectedProfileId) return;

    const predefinedTasks = getPredefinedTasks(selectedProfileId);
    predefinedTasks.forEach(task => {
      dispatch(createTask(task));
    });

    toast({
      title: t('tasks.taskCreated'),
      description: 'Predefined tasks have been added to your list',
      status: 'success',
      duration: 2000,
      position: 'top'
    });
  };

  if (!selectedProfileId) {
    return <ProfileSelection />;
  }

  const renderSection = () => {
    switch (currentSection) {
      case 'tasks':
        return (
          <>
            <Box mb={4} display="flex" justifyContent="flex-end" gap={4}>
              {profileTasks.length === 0 && canCreateTasks && (
                <Button
                  leftIcon={<StarIcon />}
                  colorScheme="purple"
                  onClick={handleAddPredefinedTasks}
                  variant="outline"
                >
                  Add Activity List
                </Button>
              )}
              {canCreateTasks && (
                <Button
                  leftIcon={<AddIcon />}
                  colorScheme="primary"
                  onClick={onTaskFormOpen}
                >
                  Create New Task
                </Button>
              )}
            </Box>
            <TaskList />
          </>
        );
      case 'achievements':
        return <AchievementList />;
      case 'rewards':
        return (
          <Box p={6}>
            <Heading size="lg" mb={4}>Rewards</Heading>
            <Text>Coming soon...</Text>
          </Box>
        );
      default:
        return (
          <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
            <Button
              as={Box}
              p={6}
              bg="white"
              borderRadius="xl"
              boxShadow="md"
              onClick={() => setCurrentSection('tasks')}
              _hover={{
                transform: 'translateY(-4px)',
                boxShadow: 'lg',
              }}
              _active={{
                transform: 'translateY(-2px)',
                boxShadow: 'md',
              }}
              transition="all 0.2s"
              display="block"
              textAlign="left"
            >
              <Heading size="md" mb={3} color="primary.600">
                Daily Tasks
              </Heading>
              <Text color="gray.600">
                Complete your daily tasks to earn points and rewards.
              </Text>
            </Button>

            <Button
              as={Box}
              p={6}
              bg="white"
              borderRadius="xl"
              boxShadow="md"
              onClick={() => setCurrentSection('achievements')}
              _hover={{
                transform: 'translateY(-4px)',
                boxShadow: 'lg',
              }}
              _active={{
                transform: 'translateY(-2px)',
                boxShadow: 'md',
              }}
              transition="all 0.2s"
              display="block"
              textAlign="left"
            >
              <Heading size="md" mb={3} color="primary.600">
                Achievements
              </Heading>
              <Text color="gray.600">
                View your earned badges and track your progress.
              </Text>
            </Button>

            <Button
              as={Box}
              p={6}
              bg="white"
              borderRadius="xl"
              boxShadow="md"
              onClick={() => canRedeemRewards && setCurrentSection('rewards')}
              _hover={canRedeemRewards ? {
                transform: 'translateY(-4px)',
                boxShadow: 'lg',
              } : undefined}
              _active={canRedeemRewards ? {
                transform: 'translateY(-2px)',
                boxShadow: 'md',
              } : undefined}
              opacity={canRedeemRewards ? 1 : 0.5}
              cursor={canRedeemRewards ? 'pointer' : 'not-allowed'}
              transition="all 0.2s"
              display="block"
              textAlign="left"
            >
              <Heading size="md" mb={3} color={canRedeemRewards ? "primary.600" : "gray.400"}>
                Rewards {!canRedeemRewards && '(Disabled)'}
              </Heading>
              <Text color="gray.600">
                See what rewards you can earn with your points.
              </Text>
            </Button>
          </SimpleGrid>
        );
    }
  };

  return (
    <VStack spacing={8} align="stretch">
      <VStack spacing={2} mb={8}>
        <Box textAlign="center">
          <Heading color="primary.500" mb={4}>
            Kids Activity Tracker
          </Heading>
          <Text fontSize="lg" color="gray.600">
            Track your daily activities and earn rewards!
          </Text>
        </Box>
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            dispatch(selectProfile(''));
          }}
        >
          Change Profile
        </Button>
      </VStack>

      <VStack spacing={8} align="stretch">
        <VStack spacing={4} w="100%">
          <ProgressStats />
          <HStack spacing={4} w="100%">
            <Box flex={1}>
              <StreakTracker {...streakProps} />
            </Box>
            <Box flex={1}>
              <PointsDisplay points={points} />
            </Box>
          </HStack>
        </VStack>

        {currentSection !== 'home' ? (
          <VStack align="stretch" spacing={4}>
            <Button onClick={() => setCurrentSection('home')} variant="link" mb={0}>
              ← Back to Menu
            </Button>
            {renderSection()}
          </VStack>
        ) : (
          renderSection()
        )}
      </VStack>

      <Box
        position="fixed"
        bottom={{ base: 2, md: 4 }}
        right={{ base: 2, md: 4 }}
        zIndex={10}
      >
        <Button
          colorScheme="gray"
          size={{ base: "sm", md: "md" }}
          variant="solid"
          onClick={onPinDialogOpen}
          shadow="md"
          _hover={{
            shadow: "lg",
            transform: "translateY(-2px)",
          }}
          transition="all 0.2s"
        >
          Parent Mode
        </Button>
      </Box>

      <TaskForm isOpen={isTaskFormOpen} onClose={onTaskFormClose} />

      <PinProtection
        isOpen={isPinDialogOpen}
        onClose={onPinDialogClose}
        onSuccess={() => {
          onPinDialogClose();
          navigate('/parent');
        }}
        title="Enter Parent PIN"
      />

      <LevelUpNotification
        isOpen={showLevelUp}
        onClose={closeLevelUpNotification}
        level={newLevel}
      />
    </VStack>
  );
};

export default Home;