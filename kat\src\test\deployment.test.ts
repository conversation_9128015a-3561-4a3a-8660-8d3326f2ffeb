import { describe, test, expect, vi, beforeEach } from 'vitest';
import { analytics } from '../utils/analytics';
import { type Plugin } from 'vite';
import packageJson from '../../package.json';

describe('Deployment Configuration', () => {
  test('package.json contains required deployment scripts', () => {
    expect(packageJson.scripts).toHaveProperty('build');
    expect(packageJson.scripts).toHaveProperty('preview');
    expect(packageJson.scripts).toHaveProperty('deploy');
  });

  test('has required dependencies for PWA', () => {
    const allDependencies = {
      ...packageJson.dependencies,
      ...packageJson.devDependencies,
    };
    expect(allDependencies).toHaveProperty('vite-plugin-pwa');
  });

  test('has analytics dependencies', () => {
    const allDependencies = {
      ...packageJson.dependencies,
      ...packageJson.devDependencies,
    };
    expect(allDependencies).toHaveProperty('rollup-plugin-visualizer');
  });
});

describe('Analytics Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorage.clear();
  });

  test('analytics is disabled by default', () => {
    expect(analytics.isEnabled()).toBe(false);
  });

  test('can enable analytics with user consent', () => {
    analytics.enable();
    expect(analytics.isEnabled()).toBe(true);
    expect(localStorage.getItem('analytics_enabled')).toBe('true');
  });

  test('can disable analytics', () => {
    analytics.enable();
    analytics.disable();
    expect(analytics.isEnabled()).toBe(false);
    expect(localStorage.getItem('analytics_enabled')).toBe('false');
  });

  test('generates anonymous user ID when enabled', () => {
    analytics.enable();
    const userId = localStorage.getItem('anonymous_user_id');
    expect(userId).toBeTruthy();
    expect(typeof userId).toBe('string');
  });

  test('removes user ID when disabled', () => {
    analytics.enable();
    analytics.disable();
    expect(localStorage.getItem('anonymous_user_id')).toBeNull();
  });

  test('tracks events when enabled', () => {
    const fetchMock = vi.spyOn(global, 'fetch').mockImplementation(() => 
      Promise.resolve(new Response())
    );

    analytics.enable();
    analytics.trackEvent('achievement_unlocked', { achievementId: 'test' });

    expect(fetchMock).toHaveBeenCalledWith('/api/analytics', expect.any(Object));
    expect(JSON.parse(fetchMock.mock.calls[0][1]!.body as string)).toMatchObject({
      event: 'achievement_unlocked',
      data: expect.objectContaining({
        achievementId: 'test',
        userId: expect.any(String),
      }),
    });
  });

  test('does not track events when disabled', () => {
    const fetchMock = vi.spyOn(global, 'fetch');
    
    analytics.disable();
    analytics.trackEvent('achievement_unlocked', { achievementId: 'test' });

    expect(fetchMock).not.toHaveBeenCalled();
  });

  test('handles errors gracefully', () => {
    const consoleSpy = vi.spyOn(console, 'debug').mockImplementation(() => {});
    vi.spyOn(global, 'fetch').mockImplementation(() => Promise.reject('Network error'));

    analytics.enable();
    analytics.trackEvent('achievement_unlocked', { achievementId: 'test' });

    expect(consoleSpy).toHaveBeenCalledWith(
      'Analytics error:',
      expect.anything()
    );
  });
});

describe('Build Configuration', () => {
  test('has correct build configuration in package.json', () => {
    expect(packageJson.scripts.build).toContain('vite.config.prod.ts');
  });

  test('has production build settings', () => {
    expect(packageJson.scripts.build).toContain('--config');
    expect(packageJson.scripts.build).toContain('tsc --noEmit');
  });

  test('has deployment settings', () => {
    expect(packageJson.homepage).toBeDefined();
    expect(packageJson.scripts.predeploy).toBeDefined();
    expect(packageJson.scripts.deploy).toBeDefined();
  });
});