/**
 * Convert a File object to a Base64 string
 */
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
};

/**
 * Validate image file
 */
export const validateImage = (file: File): { isValid: boolean; error?: string } => {
  // Check file type
  if (!file.type.startsWith('image/')) {
    return { isValid: false, error: 'Please upload an image file (PNG, JPEG, etc.)' };
  }

  // Check file size (max 2MB)
  const maxSize = 2 * 1024 * 1024; // 2MB
  if (file.size > maxSize) {
    return { isValid: false, error: 'Image size should be less than 2MB' };
  }

  return { isValid: true };
};

/**
 * Process image for upload
 */
export const processImageUpload = async (file: File): Promise<{ data?: string; error?: string }> => {
  const validation = validateImage(file);
  if (!validation.isValid) {
    return { error: validation.error };
  }

  try {
    const base64Data = await fileToBase64(file);
    return { data: base64Data };
  } catch (error) {
    return { error: 'Failed to process image. Please try again.' };
  }
};

/**
 * Resize image dimensions if needed
 */
export const getImageDimensions = (file: File): Promise<{ width: number; height: number }> => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      URL.revokeObjectURL(img.src); // Clean up
      resolve({ width: img.width, height: img.height });
    };
    img.src = URL.createObjectURL(file);
  });
};