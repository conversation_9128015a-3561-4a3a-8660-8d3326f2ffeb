import { openDB, IDBPDatabase } from 'idb';

interface CacheOptions {
  expirationTime?: number; // Time in milliseconds
  version?: number;
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

class Cache {
  private dbName = 'kat-cache';
  private dbVersion = 1;
  private db: Promise<IDBPDatabase>;

  constructor(options?: CacheOptions) {
    this.dbVersion = options?.version || this.dbVersion;
    this.db = this.initDatabase();
  }

  private async initDatabase(): Promise<IDBPDatabase> {
    return openDB(this.dbName, this.dbVersion, {
      upgrade(db) {
        if (!db.objectStoreNames.contains('cache')) {
          db.createObjectStore('cache');
        }
      },
    });
  }

  async set<T>(key: string, data: T, expirationTime?: number): Promise<void> {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
    };

    const db = await this.db;
    await db.put('cache', entry, key);
  }

  async get<T>(key: string, maxAge?: number): Promise<T | null> {
    try {
      const db = await this.db;
      const entry = await db.get('cache', key) as CacheEntry<T> | undefined;

      if (!entry) {
        return null;
      }

      // Check if data is expired
      if (maxAge && Date.now() - entry.timestamp > maxAge) {
        await this.delete(key);
        return null;
      }

      return entry.data;
    } catch (error) {
      console.warn('Cache retrieval failed:', error);
      return null;
    }
  }

  async delete(key: string): Promise<void> {
    const db = await this.db;
    await db.delete('cache', key);
  }

  async clear(): Promise<void> {
    const db = await this.db;
    await db.clear('cache');
  }

  // Utility method to remove expired entries
  async cleanup(maxAge: number): Promise<void> {
    const db = await this.db;
    const currentTime = Date.now();
    const tx = db.transaction('cache', 'readwrite');
    const store = tx.objectStore('cache');
    const keys = await store.getAllKeys();

    for (const key of keys) {
      const entry = await store.get(key) as CacheEntry<unknown>;
      if (currentTime - entry.timestamp > maxAge) {
        await store.delete(key);
      }
    }

    await tx.done;
  }
}

// Export a singleton instance
export const cache = new Cache();

// Helper functions for common caching patterns
export async function withCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: { maxAge?: number; forceRefresh?: boolean } = {}
): Promise<T> {
  const { maxAge, forceRefresh } = options;

  if (!forceRefresh) {
    const cached = await cache.get<T>(key, maxAge);
    if (cached !== null) {
      return cached;
    }
  }

  const fresh = await fetcher();
  await cache.set(key, fresh);
  return fresh;
}

// Decorator for caching class methods
export function cached(
  key: string | ((target: any, args: any[]) => string),
  options: { maxAge?: number } = {}
): MethodDecorator {
  return function (
    target: any,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const cacheKey = typeof key === 'function' ? key(this, args) : key;
      return withCache(
        cacheKey,
        () => originalMethod.apply(this, args),
        options
      );
    };

    return descriptor;
  };
}