import React from 'react';
import {
  Box,
  SimpleGrid,
  Text,
  VStack,
  Heading,
  useColorModeValue,
  Progress,
  Image,
  useBreakpointValue,
  Skeleton,
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { useAchievementData } from '../../hooks/useAchievementData';
import ErrorBoundary from '../../components/ErrorBoundary';

const MotionBox = motion(Box);

const AchievementList = () => {
  const selectedProfileId = useSelector((state: RootState) => state.profile.selectedProfileId);
  const {
    achievements,
    userAchievements,
    achievementStats,
    isLoading,
    error
  } = useAchievementData(selectedProfileId || '');

  const bgGradient = useColorModeValue(
    'linear(to-br, white, gray.50)',
    'linear(to-br, gray.800, gray.900)'
  );

  const imageSize = useBreakpointValue({ base: '60px', md: '80px' });
  const gridColumns = useBreakpointValue({ base: 1, sm: 2, md: 3 });

  if (error) {
    return (
      <Box p={4} textAlign="center">
        <Text color="red.500">Failed to load achievements. Please try again later.</Text>
      </Box>
    );
  }

  return (
    <ErrorBoundary>
      <Box p={4}>
        <VStack spacing={6} align="stretch">
          <VStack spacing={2} align="center">
            <Heading size="lg" textAlign="center" color="primary.500">
              Achievements
            </Heading>
            <Text fontSize="lg" color="gray.600">
              Track your progress and unlock special achievements!
            </Text>
            {!isLoading && (
              <Text fontSize="md" color="gray.500">
                {achievementStats.unlocked} of {achievementStats.total} unlocked (
                {achievementStats.percentComplete}%)
              </Text>
            )}
          </VStack>

          <SimpleGrid columns={gridColumns} spacing={4}>
            {achievements.map((achievement) => {
              const isUnlocked = userAchievements.some(
                ua => ua.achievementId === achievement.id
              );
              const unlockedDate = userAchievements.find(
                ua => ua.achievementId === achievement.id
              )?.unlockedAt;

              return (
                <MotionBox
                  key={achievement.id}
                  p={6}
                  bgGradient={bgGradient}
                  borderRadius="xl"
                  boxShadow="md"
                  border="1px solid"
                  borderColor={isUnlocked ? "yellow.400" : "gray.200"}
                  opacity={isUnlocked ? 1 : 0.7}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  transition={{ duration: 0.2 }}
                  position="relative"
                  overflow="hidden"
                  role="group"
                  aria-label={`Achievement: ${achievement.title}`}
                  _before={isUnlocked ? {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: 'linear-gradient(45deg, rgba(255, 215, 0, 0.1), transparent)',
                    opacity: 0.3,
                    zIndex: 0,
                  } : undefined}
                >
                  <VStack spacing={3} align="center" position="relative" zIndex={1}>
                    <Skeleton isLoaded={!isLoading} borderRadius="xl">
                      <Box
                        width={imageSize}
                        height={imageSize}
                        position="relative"
                        filter={!isUnlocked ? "grayscale(1)" : undefined}
                      >
                        <Image
                          src={achievement.imageUrl}
                          alt={achievement.title}
                          width="100%"
                          height="100%"
                          objectFit="contain"
                          loading="lazy"
                        />
                      </Box>
                    </Skeleton>

                    <Skeleton isLoaded={!isLoading}>
                      <Heading size="md" textAlign="center">
                        {achievement.title}
                      </Heading>
                    </Skeleton>

                    <Skeleton isLoaded={!isLoading}>
                      <Text fontSize="sm" color="gray.500" textAlign="center">
                        {achievement.description}
                      </Text>
                    </Skeleton>

                    {isUnlocked ? (
                      <Skeleton isLoaded={!isLoading}>
                        <Text fontSize="xs" color="green.500">
                          Unlocked {new Date(unlockedDate!).toLocaleDateString()}
                        </Text>
                      </Skeleton>
                    ) : (
                      <VStack w="100%" spacing={2}>
                        <Skeleton isLoaded={!isLoading}>
                          <Text fontSize="xs" color="gray.500">
                            {achievement.condition.type === 'TASKS_COMPLETED' &&
                              `Complete ${achievement.condition.value} tasks`}
                            {achievement.condition.type === 'POINTS_EARNED' &&
                              `Earn ${achievement.condition.value} points`}
                            {achievement.condition.type === 'STREAK_MAINTAINED' &&
                              `Maintain a ${achievement.condition.value}-day streak`}
                          </Text>
                        </Skeleton>

                        <Skeleton isLoaded={!isLoading} w="100%">
                          <Box w="100%">
                            <Progress
                              size="xs"
                              colorScheme="primary"
                              borderRadius="full"
                              value={achievementStats.percentComplete}
                            />
                          </Box>
                        </Skeleton>
                      </VStack>
                    )}
                  </VStack>
                </MotionBox>
              );
            })}
          </SimpleGrid>
        </VStack>
      </Box>
    </ErrorBoundary>
  );
};

export default AchievementList;