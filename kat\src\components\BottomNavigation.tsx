import React from 'react';
import {
  Box,
  HStack,
  VStack,
  Text,
  useBreakpointValue,
} from '@chakra-ui/react';
import { useLocation, useNavigate } from 'react-router-dom';
import { FiHome, FiCheckSquare, <PERSON><PERSON>ward, <PERSON><PERSON>ser, FiSettings } from 'react-icons/fi';
import { useTheme, useThemeStyles } from '../styles/themes/ThemeContext';
import { useThemedAnimation } from '../hooks/useThemedAnimation';

interface NavItemProps {
  icon: React.ElementType;
  label: string;
  isActive: boolean;
  onClick: () => void;
}

const NavItem: React.FC<NavItemProps> = ({ icon: Icon, label, isActive, onClick }) => {
  const { currentTheme } = useTheme();
  const bounceAnimation = useThemedAnimation('bounceScale', {
    duration: '0.3s',
  });

  return (
    <VStack
      spacing={1}
      px={4}
      py={2}
      cursor="pointer"
      color={isActive ? currentTheme.colors.text.accent : currentTheme.colors.text.secondary}
      transition="all 0.2s ease-in-out"
      onClick={onClick}
      position="relative"
      role="button"
      aria-pressed={isActive}
      _hover={{
        color: currentTheme.colors.text.accent,
        transform: 'translateY(-2px)',
      }}
      _active={{
        transform: 'scale(0.95)',
      }}
    >
      <Box
        position="relative"
        {...(isActive && bounceAnimation)}
      >
        <Icon size={24} />
        {isActive && (
          <Box
            position="absolute"
            bottom="-8px"
            left="50%"
            transform="translateX(-50%)"
            width="4px"
            height="4px"
            borderRadius="full"
            bg={currentTheme.colors.button.bg}
            className="nav-dot"
          />
        )}
      </Box>
      <Text
        fontSize="xs"
        fontWeight={isActive ? 'bold' : 'medium'}
        display={{ base: 'none', sm: 'block' }}
      >
        {label}
      </Text>
    </VStack>
  );
};

export const BottomNavigation = () => {
  const { currentTheme } = useTheme();
  const { cardStyle } = useThemeStyles();
  const location = useLocation();
  const navigate = useNavigate();
  const showOnMobile = useBreakpointValue({ base: true, lg: false });

  const navItems = [
    { path: '/', label: 'Home', icon: FiHome },
    { path: '/tasks', label: 'Tasks', icon: FiCheckSquare },
    { path: '/achievements', label: 'Achievements', icon: FiAward },
    { path: '/profile', label: 'Profile', icon: FiUser },
    { path: '/parent', label: 'Parent', icon: FiSettings },
  ];

  if (!showOnMobile) return null;

  return (
    <Box
      position="fixed"
      bottom={0}
      left={0}
      right={0}
      zIndex={1000}
      {...cardStyle}
      borderRadius="20px 20px 0 0"
      py={2}
      sx={{
        backdropFilter: 'blur(10px)',
        borderTop: '1px solid',
        borderColor: currentTheme.colors.card.border,
        boxShadow: `0 -4px 20px ${currentTheme.colors.card.glow}`,
      }}
    >
      <HStack
        justify="space-around"
        align="center"
        maxW="container.lg"
        mx="auto"
        px={4}
      >
        {navItems.map((item) => (
          <NavItem
            key={item.path}
            icon={item.icon}
            label={item.label}
            isActive={location.pathname === item.path}
            onClick={() => navigate(item.path)}
          />
        ))}
      </HStack>
    </Box>
  );
};

export default BottomNavigation;