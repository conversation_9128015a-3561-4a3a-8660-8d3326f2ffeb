import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON>,
  <PERSON>uItem,
  Text,
  HStack,
  useToast,
} from '@chakra-ui/react';
import { ChevronDownIcon } from '@chakra-ui/icons';
import { useTranslation } from 'react-i18next';
import { SUPPORTED_LANGUAGES, type SupportedLanguage } from '../i18n';

// Language flag emojis
const LANGUAGE_FLAGS: Record<SupportedLanguage, string> = {
  en: '🇺🇸',
  pl: '🇵🇱',
  is: '🇮🇸',
};

interface LanguageSwitcherProps {
  variant?: 'button' | 'menu';
  size?: 'sm' | 'md' | 'lg';
  showFlag?: boolean;
  showText?: boolean;
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  variant = 'menu',
  size = 'md',
  showFlag = true,
  showText = true,
}) => {
  const { t, i18n } = useTranslation();
  const toast = useToast();

  const currentLanguage = i18n.language as SupportedLanguage;

  const handleLanguageChange = async (language: SupportedLanguage) => {
    try {
      await i18n.changeLanguage(language);

      // Show success toast
      toast({
        title: t('language.changed'),
        status: 'success',
        duration: 2000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Failed to change language:', error);
      toast({
        title: t('errors.generic'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const getCurrentLanguageDisplay = () => (
    <HStack spacing={2}>
      {showFlag && <Text fontSize={size}>{LANGUAGE_FLAGS[currentLanguage]}</Text>}
      {showText && (
        <Text fontSize={size}>
          {SUPPORTED_LANGUAGES[currentLanguage]}
        </Text>
      )}
    </HStack>
  );

  if (variant === 'button') {
    return (
      <HStack spacing={2}>
        {Object.entries(SUPPORTED_LANGUAGES).map(([code, name]) => (
          <Button
            key={code}
            size={size}
            variant={currentLanguage === code ? 'solid' : 'outline'}
            onClick={() => handleLanguageChange(code as SupportedLanguage)}
            leftIcon={showFlag ? <Text>{LANGUAGE_FLAGS[code as SupportedLanguage]}</Text> : undefined}
          >
            {showText ? name : LANGUAGE_FLAGS[code as SupportedLanguage]}
          </Button>
        ))}
      </HStack>
    );
  }

  return (
    <Menu>
      <MenuButton
        as={Button}
        rightIcon={<ChevronDownIcon />}
        size={size}
        variant="outline"
        aria-label={t('language.select')}
      >
        {getCurrentLanguageDisplay()}
      </MenuButton>
      <MenuList>
        {Object.entries(SUPPORTED_LANGUAGES).map(([code, name]) => (
          <MenuItem
            key={code}
            onClick={() => handleLanguageChange(code as SupportedLanguage)}
            icon={<Text>{LANGUAGE_FLAGS[code as SupportedLanguage]}</Text>}
            isDisabled={currentLanguage === code}
          >
            <Text>{name}</Text>
          </MenuItem>
        ))}
      </MenuList>
    </Menu>
  );
};

export default LanguageSwitcher;
