// Mocks must be at the top, before any imports
import { vi } from 'vitest';

vi.mock('canvas-confetti', () => ({
  __esModule: true,
  default: vi.fn().mockImplementation(() => Promise.resolve())
}));

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { getProgressPercentage, triggerAchievementAnimation } from './achievementAnimations';
import confetti from 'canvas-confetti';

describe('achievementAnimations', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('getProgressPercentage', () => {
    it('calculates correct percentage', () => {
      expect(getProgressPercentage(5, 10)).toBe(50);
      expect(getProgressPercentage(7, 10)).toBe(70);
      expect(getProgressPercentage(10, 10)).toBe(100);
    });

    it('handles zero target value', () => {
      expect(getProgressPercentage(5, 0)).toBe(100);
    });

    it('caps percentage at 100', () => {
      expect(getProgressPercentage(15, 10)).toBe(100);
    });

    it('rounds percentage to nearest integer', () => {
      expect(getProgressPercentage(3, 7)).toBe(43); // 42.857... rounded to 43
    });
  });

  describe('triggerAchievementAnimation', () => {
    it('launches confetti at intervals', () => {
      const now = new Date('2025-01-01').getTime();
      vi.setSystemTime(now);
      
      triggerAchievementAnimation();
      
      // Initial state at t=0
      expect(confetti).toHaveBeenCalledTimes(0);

      // First interval (t=250ms)
      vi.advanceTimersByTime(250);
      expect(confetti).toHaveBeenCalledWith(expect.objectContaining({
        startVelocity: 30,
        spread: 360,
        ticks: 60,
        zIndex: 10000,
        particleCount: 43.75,
        origin: { x: 0.2, y: 0.5 },
        colors: ['#FFD700', '#FFA500', '#FF6B6B'],
      }));
      expect(confetti).toHaveBeenCalledWith(expect.objectContaining({
        origin: { x: 0.8, y: 0.5 },
      }));
      expect(confetti).toHaveBeenCalledTimes(2);

      // Advance to middle (t=1000ms)
      vi.advanceTimersByTime(750);
      expect(confetti).toHaveBeenCalledTimes(8); // 4 intervals * 2 sides

      // Complete animation (t=2000ms)
      vi.advanceTimersByTime(1000);
      expect(confetti).toHaveBeenCalledTimes(14); // 7 intervals * 2 sides
    });

    it('clears interval when animation ends', () => {
      const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
      
      triggerAchievementAnimation();
      vi.advanceTimersByTime(2000);

      expect(clearIntervalSpy).toHaveBeenCalled();
    });
  });
});