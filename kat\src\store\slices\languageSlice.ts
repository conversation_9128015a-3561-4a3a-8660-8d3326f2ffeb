import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { STORAGE_KEYS, StorageManager } from '../../utils/storage';
import { SUPPORTED_LANGUAGES, type SupportedLanguage } from '../../i18n';
import type { RootState } from '../index';

interface LanguageState {
  currentLanguage: SupportedLanguage;
  isLoading: boolean;
  error: string | null;
}

// Get initial language from storage or browser
const getInitialLanguage = (): SupportedLanguage => {
  // Try to get from localStorage first
  const storedLanguage = StorageManager.get<string>(STORAGE_KEYS.LANGUAGE, '');
  if (storedLanguage && storedLanguage in SUPPORTED_LANGUAGES) {
    return storedLanguage as SupportedLanguage;
  }

  // Try to detect from browser language
  const browserLanguage = navigator.language.split('-')[0];
  if (browserLanguage in SUPPORTED_LANGUAGES) {
    return browserLanguage as SupportedLanguage;
  }

  // Default to English
  return 'en';
};

const initialState: LanguageState = {
  currentLanguage: getInitialLanguage(),
  isLoading: false,
  error: null,
};

const languageSlice = createSlice({
  name: 'language',
  initialState,
  reducers: {
    setLanguageStart: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    setLanguageSuccess: (state, action: PayloadAction<SupportedLanguage>) => {
      state.currentLanguage = action.payload;
      state.isLoading = false;
      state.error = null;
      
      // Persist to localStorage
      StorageManager.set(STORAGE_KEYS.LANGUAGE, action.payload);
    },
    setLanguageError: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.error = action.payload;
    },
    clearLanguageError: (state) => {
      state.error = null;
    },
  },
});

export const {
  setLanguageStart,
  setLanguageSuccess,
  setLanguageError,
  clearLanguageError,
} = languageSlice.actions;

// Selectors
export const selectCurrentLanguage = (state: RootState) => state.language.currentLanguage;
export const selectLanguageLoading = (state: RootState) => state.language.isLoading;
export const selectLanguageError = (state: RootState) => state.language.error;

// Async action for changing language
export const changeLanguage = (language: SupportedLanguage) => async (dispatch: any) => {
  try {
    dispatch(setLanguageStart());
    
    // Validate language
    if (!(language in SUPPORTED_LANGUAGES)) {
      throw new Error(`Unsupported language: ${language}`);
    }
    
    // Update i18n instance (this will be handled by the component)
    dispatch(setLanguageSuccess(language));
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to change language';
    dispatch(setLanguageError(errorMessage));
  }
};

export default languageSlice.reducer;
