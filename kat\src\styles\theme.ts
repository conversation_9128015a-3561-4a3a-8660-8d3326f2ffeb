import { theme as baseTheme } from '@chakra-ui/theme'
import type { ThemeConfig } from '@chakra-ui/theme'
import { extendTheme } from '@chakra-ui/react'
import { bounceScale, glowPulse, sparkle } from './themes/animations'

const config: ThemeConfig = {
  initialColorMode: 'light',
  useSystemColorMode: false,
}

const colors = {
  primary: {
    50: '#E6FFFA',
    100: '#B3FFE6',
    200: '#80FFD1',
    300: '#33FFBB',
    400: '#00FFA6',
    500: '#00E699',
    600: '#00CC8C',
    700: '#00B380',
    800: '#009973',
    900: '#006652',
  },
  secondary: {
    50: '#FFE5F2',
    100: '#FFB3D9',
    200: '#FF80C0',
    300: '#FF47A1',
    400: '#FF1A8C',
    500: '#FF0077',
    600: '#E6006B',
    700: '#CC005F',
    800: '#B30054',
    900: '#800040',
  },
}

const theme = extendTheme({
  config,
  colors: {
    ...baseTheme.colors,
    ...colors,
  },
  fonts: {
    heading: '"Fredoka One", system-ui, sans-serif',
    body: '"Comic Sans MS", "Comic Sans", system-ui, sans-serif',
  },
  components: {
    Button: {
      baseStyle: {
        borderRadius: 'full',
        fontWeight: 'bold',
        position: 'relative',
        transition: 'all 0.3s ease',
        _before: {
          content: '""',
          position: 'absolute',
          top: '-2px',
          left: '-2px',
          right: '-2px',
          bottom: '-2px',
          borderRadius: 'full',
          opacity: '0',
          transition: 'opacity 0.3s ease',
        },
      },
      variants: {
        solid: {
          animation: `${glowPulse} 2s infinite`,
          _hover: {
            animation: `${sparkle} 1s infinite`,
            transform: 'scale(1.05)',
            _before: {
              opacity: '1',
            },
          },
          _active: {
            animation: `${bounceScale} 0.5s`,
            transform: 'scale(0.95)',
          },
        },
      },
      defaultProps: {
        colorScheme: 'primary',
      },
    },
    Card: {
      baseStyle: {
        borderRadius: 'xl',
        overflow: 'hidden',
        transition: 'all 0.3s ease',
        _hover: {
          transform: 'translateY(-5px)',
        },
      },
    },
    Link: {
      baseStyle: {
        transition: 'all 0.2s ease',
        _hover: {
          textDecoration: 'none',
          transform: 'scale(1.05)',
        },
      },
    },
  },
  styles: {
    global: {
      body: {
        transition: 'background-color 0.3s ease',
      },
      'button, a': {
        cursor: 'pointer',
        _disabled: {
          cursor: 'not-allowed',
        },
      },
    },
  },
})

export default theme