import { ThemeConfig } from '@chakra-ui/theme';

export type ThemeVariant = 'space' | 'candy' | 'neon';

export interface ThemeColors {
  bg: {
    primary: string;
    secondary: string;
    pattern: string;
  };
  card: {
    bg: string;
    border: string;
    hover: string;
    glow: string;
  };
  button: {
    bg: string;
    hover: string;
    text: string;
    glow: string;
  };
  text: {
    primary: string;
    secondary: string;
    accent: string;
  };
  achievement: {
    locked: string;
    unlocked: string;
    progress: string;
  };
  stats: {
    bg: string;
    border: string;
    text: string;
  };
  nav: {
    active: string;
    inactive: string;
    bg: string;
    indicator: string;
    glow: string;
  };
}

export interface CustomTheme {
  name: string;
  colors: ThemeColors;
  patterns: {
    background: string;
    card: string;
    nav?: string;
  };
  animations: {
    button: string;
    card: string;
    achievement: string;
    nav?: string;
  };
  effects: {
    glow: string;
    hover: string;
    active: string;
  };
  config: ThemeConfig;
}