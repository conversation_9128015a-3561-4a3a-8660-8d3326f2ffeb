import React from 'react';
import {
  Box,
  Text,
  VStack,
  Progress,
  HStack,
  Icon,
  Tooltip,
} from '@chakra-ui/react';
import { FiAward } from 'react-icons/fi';
import { motion } from 'framer-motion';

const MotionBox = motion(Box);

interface PointsDisplayProps {
  points: number;
}

import { LEVEL_THRESHOLDS, calculateLevel } from '../constants/levels';

const PointsDisplay: React.FC<PointsDisplayProps> = ({ points }) => {
  const { level, progress } = calculateLevel(points);
  const nextLevelPoints = LEVEL_THRESHOLDS[level] || LEVEL_THRESHOLDS[level - 1] * 2;

  return (
    <Box
      bg="white"
      p={4}
      borderRadius="lg"
      boxShadow="sm"
      border="1px solid"
      borderColor="gray.100"
    >
      <VStack spacing={3} align="stretch">
        <HStack justify="space-between" align="center">
          <VStack align="start" spacing={0}>
            <Text fontSize="sm" color="gray.500">
              Total Points
            </Text>
            <Text fontSize="2xl" fontWeight="bold" color="primary.500">
              {points}
            </Text>
          </VStack>
          <Tooltip label={`Level ${level}`} placement="top">
            <MotionBox
              initial={{ scale: 1 }}
              whileHover={{ scale: 1.1 }}
              display="flex"
              alignItems="center"
              justifyContent="center"
              bg="yellow.100"
              color="yellow.600"
              borderRadius="full"
              w="40px"
              h="40px"
            >
              <Icon as={FiAward} boxSize={5} />
            </MotionBox>
          </Tooltip>
        </HStack>

        <VStack spacing={1} align="stretch">
          <HStack justify="space-between">
            <Text fontSize="xs" color="gray.500">
              Level {level}
            </Text>
            <Text fontSize="xs" color="gray.500">
              {points} / {nextLevelPoints}
            </Text>
          </HStack>
          <Progress
            value={progress}
            size="sm"
            colorScheme="yellow"
            borderRadius="full"
            hasStripe
            isAnimated
          />
        </VStack>
      </VStack>
    </Box>
  );
};

export default PointsDisplay;