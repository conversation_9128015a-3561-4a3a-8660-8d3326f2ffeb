import React from 'react';
import { render, screen } from '@testing-library/react';
import { ChakraProvider } from '@chakra-ui/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import LanguageSwitcher from './LanguageSwitcher';
import languageReducer from '../store/slices/languageSlice';
import '../i18n'; // Initialize i18n

// Create a test store
const createTestStore = () => {
  return configureStore({
    reducer: {
      language: languageReducer,
    },
  });
};

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const store = createTestStore();
  
  return (
    <Provider store={store}>
      <ChakraProvider>
        {children}
      </ChakraProvider>
    </Provider>
  );
};

describe('LanguageSwitcher', () => {
  it('renders language switcher menu', () => {
    render(
      <TestWrapper>
        <LanguageSwitcher />
      </TestWrapper>
    );

    // Should show current language (English by default)
    expect(screen.getByText('English')).toBeInTheDocument();
  });

  it('renders language switcher as buttons', () => {
    render(
      <TestWrapper>
        <LanguageSwitcher variant="button" />
      </TestWrapper>
    );

    // Should show all language buttons
    expect(screen.getByText('English')).toBeInTheDocument();
    expect(screen.getByText('Polski')).toBeInTheDocument();
    expect(screen.getByText('Íslenska')).toBeInTheDocument();
  });

  it('shows flags when enabled', () => {
    render(
      <TestWrapper>
        <LanguageSwitcher showFlag={true} showText={false} variant="button" />
      </TestWrapper>
    );

    // Should show flag emojis
    expect(screen.getByText('🇺🇸')).toBeInTheDocument();
    expect(screen.getByText('🇵🇱')).toBeInTheDocument();
    expect(screen.getByText('🇮🇸')).toBeInTheDocument();
  });
});
