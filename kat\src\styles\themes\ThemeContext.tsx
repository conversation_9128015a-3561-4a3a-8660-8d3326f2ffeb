import React, { createContext, useContext, useState, useEffect } from 'react';
import { useColorMode } from '@chakra-ui/react';
import { ThemeVariant, CustomTheme } from './types';
import { spaceTheme } from './spaceTheme';
import { candyTheme } from './candyTheme';
import { neonTheme } from './neonTheme';

interface ThemeContextType {
  currentTheme: CustomTheme;
  themeVariant: ThemeVariant;
  setThemeVariant: (variant: ThemeVariant) => void;
}

const themes: Record<ThemeVariant, CustomTheme> = {
  space: spaceTheme,
  candy: candyTheme,
  neon: neonTheme,
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
  initialTheme?: ThemeVariant;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  initialTheme = 'space',
}) => {
  const [themeVariant, setThemeVariant] = useState<ThemeVariant>(
    (localStorage.getItem('themeVariant') as ThemeVariant) || initialTheme
  );
  const { setColorMode } = useColorMode();

  const currentTheme = themes[themeVariant];

  useEffect(() => {
    localStorage.setItem('themeVariant', themeVariant);
    setColorMode(currentTheme.config.initialColorMode);

    // Apply theme-specific styles
    document.body.style.background = currentTheme.colors.bg.primary;
    document.body.style.backgroundImage = currentTheme.patterns.background;
    document.body.style.transition = 'all 0.3s ease-in-out';

    // Apply theme animations
    const style = document.createElement('style');
    style.textContent = `
      ${currentTheme.animations.button}
      ${currentTheme.animations.card}
      ${currentTheme.animations.achievement}

      .theme-button {
        animation: neonPulse 2s infinite;
      }
      
      .theme-card {
        animation: neonBorder 3s infinite;
      }
      
      .theme-achievement {
        animation: neonAchievement 2s infinite;
      }
    `;

    const existingStyle = document.querySelector('[data-theme-styles]');
    if (existingStyle) {
      existingStyle.remove();
    }
    style.setAttribute('data-theme-styles', '');
    document.head.appendChild(style);

    return () => {
      style.remove();
    };
  }, [themeVariant, currentTheme, setColorMode]);

  return (
    <ThemeContext.Provider value={{ currentTheme, themeVariant, setThemeVariant }}>
      {children}
    </ThemeContext.Provider>
  );
};

// Hook for accessing theme styles
export const useThemeStyles = () => {
  const { currentTheme } = useTheme();
  
  return {
    buttonStyle: {
      bg: currentTheme.colors.button.bg,
      color: currentTheme.colors.button.text,
      _hover: {
        bg: currentTheme.colors.button.hover,
        boxShadow: `0 0 15px ${currentTheme.colors.button.glow}`,
        transform: currentTheme.effects.hover,
      },
      _active: {
        transform: currentTheme.effects.active,
      },
      transition: 'all 0.2s ease-in-out',
    },
    cardStyle: {
      bg: currentTheme.colors.card.bg,
      borderColor: currentTheme.colors.card.border,
      boxShadow: `0 0 10px ${currentTheme.colors.card.glow}`,
      _hover: {
        bg: currentTheme.colors.card.hover,
        boxShadow: `0 0 20px ${currentTheme.colors.card.glow}`,
      },
      transition: 'all 0.3s ease-in-out',
    },
    textStyle: {
      color: currentTheme.colors.text.primary,
    },
    accentTextStyle: {
      color: currentTheme.colors.text.accent,
    },
  };
};