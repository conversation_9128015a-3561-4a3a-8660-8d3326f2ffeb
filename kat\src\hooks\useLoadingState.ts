import React, { createContext, useContext, useState, type ReactNode } from 'react';

interface LoadingState {
  isLoading: boolean;
  message?: string;
  setLoading: (loading: boolean, message?: string) => void;
}

interface LoadingProviderProps {
  children: ReactNode;
}

const defaultLoadingState: LoadingState = {
  isLoading: false,
  message: undefined,
  setLoading: () => undefined,
};

const LoadingContext = createContext<LoadingState>(defaultLoadingState);
LoadingContext.displayName = 'LoadingContext';

/**
 * Provider component for managing global loading state
 */
export const LoadingProvider: React.FC<LoadingProviderProps> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<string>();

  const setLoading = React.useCallback((loading: boolean, newMessage?: string) => {
    setIsLoading(loading);
    setMessage(newMessage);
  }, []);

  const value = React.useMemo(() => ({
    isLoading,
    message,
    setLoading,
  }), [isLoading, message, setLoading]);

  return React.createElement(LoadingContext.Provider, 
    { value },
    children
  );
};

LoadingProvider.displayName = 'LoadingProvider';

/**
 * Hook for accessing the loading state context
 */
export function useLoadingState(): LoadingState {
  const context = useContext(LoadingContext);

  if (!context) {
    throw new Error('useLoadingState must be used within a LoadingProvider');
  }

  return context;
}