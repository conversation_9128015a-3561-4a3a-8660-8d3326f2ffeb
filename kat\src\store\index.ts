import { configureStore } from '@reduxjs/toolkit'
import { setupListeners } from '@reduxjs/toolkit/query'

import profileReducer from './slices/profileSlice'
import taskReducer from './slices/taskSlice'
import achievementReducer from './slices/achievementSlice'
import parentReducer from './slices/parentSlice'
// import languageReducer from './slices/languageSlice'
import { achievementMiddleware } from './middleware/achievementMiddleware'

const store = configureStore({
  reducer: {
    profile: profileReducer,
    task: taskReducer,
    achievement: achievementReducer,
    parent: parentReducer
    // language: languageReducer
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore certain action types or paths in state for non-serializable values
        ignoredActions: ['persist/PERSIST'],
      },
    }).concat(achievementMiddleware),
})

setupListeners(store.dispatch)

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch

export { store }