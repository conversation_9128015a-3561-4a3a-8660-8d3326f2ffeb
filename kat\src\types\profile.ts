export interface Profile {
  id: string;
  name: string;
  avatarColor: string;
  avatarUrl?: string;  // New field for custom avatar images
  createdAt: number;
  points: number;
  achievements: string[];
  completedTasks: string[];
}

export interface CreateProfileData {
  name: string;
  avatarColor: string;
  avatarUrl?: string;  // New field for custom avatar images
}

export interface UpdateProfileData {
  name?: string;
  avatarColor?: string;
  avatarUrl?: string;  // New field for custom avatar images
  points?: number;
  achievements?: string[];
  completedTasks?: string[];
}