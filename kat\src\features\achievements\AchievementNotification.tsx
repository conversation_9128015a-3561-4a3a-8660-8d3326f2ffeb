import React from 'react';
import {
  Box,
  HStack,
  Text,
  Image,
  useDisclosure,
  Portal,
} from '@chakra-ui/react';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { triggerAchievementAnimation } from '../../utils/achievementAnimations';
import { playSound } from '../../utils/sounds';
import { useTheme, useThemeStyles } from '../../styles/themes/ThemeContext';
import { useThemedAnimation } from '../../hooks/useThemedAnimation';
import type { Achievement } from '../../types/achievement';

interface NotificationContentProps {
  achievement: Achievement;
  isVisible: boolean;
}

const NotificationContent: React.FC<NotificationContentProps> = ({ achievement, isVisible }) => {
  const { currentTheme } = useTheme();
  const { cardStyle } = useThemeStyles();
  const floatAnimation = useThemedAnimation('floatAnimation', {
    duration: '2s',
    iterationCount: 'infinite',
  });

  return (
    <Box
      {...cardStyle}
      p={4}
      borderWidth="2px"
      borderRadius="xl"
      maxW="400px"
      position="relative"
      overflow="hidden"
      opacity={isVisible ? 1 : 0}
      transform={isVisible ? 'translateX(0)' : 'translateX(100%)'}
      transition="all 0.3s ease-in-out"
      _before={{
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `linear-gradient(45deg, ${currentTheme.colors.card.glow} 0%, transparent 100%)`,
        opacity: 0.1,
        zIndex: 0,
      }}
    >
      <HStack spacing={4} position="relative" zIndex={1}>
        <Box
          borderRadius="full"
          p={2}
          bg={currentTheme.colors.achievement.unlocked}
          {...floatAnimation}
        >
          <Image
            src={achievement.imageUrl}
            alt={achievement.title}
            boxSize="48px"
            objectFit="cover"
            borderRadius="full"
          />
        </Box>
        <Box>
          <Text
            fontSize="lg"
            fontWeight="bold"
            color={currentTheme.colors.text.accent}
            mb={1}
          >
            Achievement Unlocked!
          </Text>
          <Text
            color={currentTheme.colors.text.primary}
            fontWeight="medium"
          >
            {achievement.title}
          </Text>
          <Text
            fontSize="sm"
            color={currentTheme.colors.text.secondary}
          >
            {achievement.description}
          </Text>
        </Box>
      </HStack>
    </Box>
  );
};

export const AchievementNotification: React.FC = () => {
  const { isOpen, onClose } = useDisclosure({ defaultIsOpen: false });

  // Get the last unlocked achievement directly from the state
  const lastUnlocked = useSelector((state: RootState) => state.achievement.lastUnlocked);
  
  React.useEffect(() => {
    if (lastUnlocked) {
      triggerAchievementAnimation();
      playSound('achievement');
      setTimeout(onClose, 5000);
    }
  }, [lastUnlocked, onClose]);

  if (!lastUnlocked || !isOpen) {
    return null;
  }

  return (
    <Portal>
      <Box
        position="fixed"
        top="24px"
        right="24px"
        zIndex="toast"
      >
        <NotificationContent 
          achievement={lastUnlocked}
          isVisible={isOpen}
        />
      </Box>
    </Portal>
  );
};

export default AchievementNotification;