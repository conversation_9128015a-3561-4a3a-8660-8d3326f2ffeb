import { CustomTheme } from './types';

export const spaceTheme: CustomTheme = {
  name: 'Space Adventure',
  colors: {
    bg: {
      primary: '#0A1128',
      secondary: '#1B2A4A',
      pattern: 'rgba(255, 255, 255, 0.03)',
    },
    card: {
      bg: 'rgba(13, 31, 97, 0.7)',
      border: '#4A6AD9',
      hover: 'rgba(24, 51, 147, 0.8)',
      glow: 'rgba(74, 106, 217, 0.4)',
    },
    button: {
      bg: '#FF6B2B',
      hover: '#FF894F',
      text: '#FFFFFF',
      glow: 'rgba(255, 107, 43, 0.4)',
    },
    text: {
      primary: '#FFFFFF',
      secondary: '#B8C7FF',
      accent: '#FFB72B',
    },
    achievement: {
      locked: 'rgba(74, 106, 217, 0.3)',
      unlocked: '#4A6AD9',
      progress: '#FF6B2B',
    },
    stats: {
      bg: 'rgba(13, 31, 97, 0.5)',
      border: '#4A6AD9',
      text: '#B8C7FF',
    },
    nav: {
      active: '#FF6B2B',
      inactive: '#B8C7FF',
      bg: 'rgba(13, 31, 97, 0.9)',
      indicator: '#FF6B2B',
      glow: 'rgba(255, 107, 43, 0.4)',
    },
  },
  patterns: {
    background: `
      radial-gradient(circle at 10% 20%, rgba(74, 106, 217, 0.05) 0%, transparent 20%),
      radial-gradient(circle at 90% 50%, rgba(255, 183, 43, 0.05) 0%, transparent 20%),
      radial-gradient(circle at 50% 80%, rgba(255, 107, 43, 0.05) 0%, transparent 20%)
    `,
    card: `
      linear-gradient(45deg, rgba(74, 106, 217, 0.1) 0%, rgba(13, 31, 97, 0.1) 100%)
    `,
    nav: `
      linear-gradient(0deg, rgba(13, 31, 97, 0.95) 0%, rgba(13, 31, 97, 0.8) 100%)
    `,
  },
  animations: {
    button: `
      @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
      }
    `,
    card: `
      @keyframes float {
        0% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
        100% { transform: translateY(0px); }
      }
    `,
    achievement: `
      @keyframes glow {
        0% { box-shadow: 0 0 5px rgba(74, 106, 217, 0.4); }
        50% { box-shadow: 0 0 20px rgba(74, 106, 217, 0.6); }
        100% { box-shadow: 0 0 5px rgba(74, 106, 217, 0.4); }
      }
    `,
    nav: `
      @keyframes navGlow {
        0% { box-shadow: 0 -2px 10px rgba(255, 107, 43, 0.2); }
        50% { box-shadow: 0 -2px 15px rgba(255, 107, 43, 0.4); }
        100% { box-shadow: 0 -2px 10px rgba(255, 107, 43, 0.2); }
      }
    `,
  },
  effects: {
    glow: 'box-shadow: 0 0 15px',
    hover: 'transform: translateY(-5px)',
    active: 'transform: scale(0.98)',
  },
  config: {
    initialColorMode: 'dark',
    useSystemColorMode: false,
  },
};