// Simple, privacy-focused analytics implementation
export type EventName = 
  | 'app_initialized'
  | 'task_completed'
  | 'achievement_unlocked'
  | 'profile_created'
  | 'streak_milestone'
  | 'points_milestone'
  | 'error_occurred';

interface EventProperties {
  [key: string]: string | number | boolean;
}

class Analytics {
  private enabled: boolean;
  private userId: string | null;

  constructor() {
    this.enabled = false;
    this.userId = null;
    this.init();
  }

  private init() {
    // Check if analytics is allowed (based on user consent)
    this.enabled = localStorage.getItem('analytics_enabled') === 'true';
    this.userId = localStorage.getItem('anonymous_user_id');

    if (this.enabled && !this.userId) {
      // Generate anonymous ID if not exists
      this.userId = crypto.randomUUID();
      localStorage.setItem('anonymous_user_id', this.userId);
    }
  }

  public enable(): void {
    this.enabled = true;
    localStorage.setItem('analytics_enabled', 'true');
    this.init();
  }

  public disable(): void {
    this.enabled = false;
    localStorage.setItem('analytics_enabled', 'false');
    this.userId = null;
    localStorage.removeItem('anonymous_user_id');
  }

  public isEnabled(): boolean {
    return this.enabled;
  }

  public trackEvent(name: EventName, properties: EventProperties = {}): void {
    if (!this.enabled) return;

    // Add basic properties
    const eventData = {
      ...properties,
      timestamp: new Date().toISOString(),
      userId: this.userId,
      path: window.location.pathname,
    };

    // In development, just log to console
    if (process.env.NODE_ENV === 'development') {
      console.log('[Analytics]', name, eventData);
      return;
    }

    // In production, send to analytics endpoint
    try {
      fetch('/api/analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          event: name,
          data: eventData,
        }),
      }).catch(() => {
        // Silently fail if analytics fails
        console.debug('Analytics event failed to send');
      });
    } catch (error) {
      // Ignore any errors in analytics
      console.debug('Analytics error:', error);
    }
  }

  public trackError(error: Error, context: string): void {
    this.trackEvent('error_occurred', {
      errorMessage: error.message,
      errorName: error.name,
      context,
      stack: error.stack || 'No stack trace',
    });
  }

  public trackAchievement(achievementId: string, profileId: string): void {
    this.trackEvent('achievement_unlocked', {
      achievementId,
      profileId,
    });
  }

  public trackTaskCompletion(taskId: string, profileId: string): void {
    this.trackEvent('task_completed', {
      taskId,
      profileId,
    });
  }

  public trackStreakMilestone(streak: number, profileId: string): void {
    this.trackEvent('streak_milestone', {
      streak,
      profileId,
    });
  }

  public trackPointsMilestone(points: number, profileId: string): void {
    this.trackEvent('points_milestone', {
      points,
      profileId,
    });
  }
}

// Export a singleton instance
export const analytics = new Analytics();