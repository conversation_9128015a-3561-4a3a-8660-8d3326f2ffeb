import React, { Suspense, ComponentType } from 'react';
import LoadingSpinner from '../components/LoadingSpinner';

interface LoadableProps {
  message?: string;
}

/**
 * Higher-order component for lazy loading components with a consistent loading experience
 * @param importPromise - Dynamic import promise from React.lazy
 * @param loadingMessage - Optional loading message to display
 * @returns Wrapped component with loading state
 */
export function lazyLoad<P extends object>(
  importPromise: Promise<{ default: ComponentType<P> }>,
  loadingMessage?: string
) {
  const LazyComponent = React.lazy(() => importPromise);

  const Loadable = React.forwardRef<any, P & LoadableProps>(
    ({ message, ...props }, ref) => (
      <Suspense
        fallback={
          <LoadingSpinner
            fullScreen={false}
            message={message || loadingMessage}
            size="md"
          />
        }
      >
        <LazyComponent ref={ref} {...(props as P)} />
      </Suspense>
    )
  );

  // Set display name for development environment
  if (process.env.NODE_ENV !== 'production') {
    const getComponentName = () => {
      return importPromise.then(module => 
        module.default.displayName || module.default.name || 'Component'
      ).catch(() => 'Component');
    };
    getComponentName().then(name => {
      Loadable.displayName = `Loadable(${name})`;
    });
  }

  return Loadable;
}

/**
 * Utility for creating route-level code splitting points
 * @param importPromise - Dynamic import promise
 * @param loadingMessage - Optional loading message
 * @returns Route component with loading state
 */
export function lazyRoute<P extends object>(
  importPromise: Promise<{ default: ComponentType<P> }>,
  loadingMessage: string = 'Loading...'
) {
  const LazyComponent = React.lazy(() => importPromise);

  const RouteComponent = React.forwardRef<any, P>(
    (props, ref) => (
      <Suspense
        fallback={
          <LoadingSpinner
            fullScreen
            message={loadingMessage}
            size="lg"
            position="fixed"
          />
        }
      >
        <LazyComponent ref={ref} {...props} />
      </Suspense>
    )
  );

  // Set display name for development environment
  if (process.env.NODE_ENV !== 'production') {
    const getComponentName = () => {
      return importPromise.then(module => 
        module.default.displayName || module.default.name || 'Component'
      ).catch(() => 'Component');
    };
    getComponentName().then(name => {
      RouteComponent.displayName = `LazyRoute(${name})`;
    });
  }

  return RouteComponent;
}