import { CreateAchievementData } from '../../types/achievement';

// Base64 encoded SVG images for achievements
const ACHIEVEMENT_IMAGES = {
  firstSteps: `data:image/svg+xml,${encodeURIComponent(`
    <svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
      <circle cx="64" cy="64" r="60" fill="#4299E1"/>
      <circle cx="64" cy="64" r="50" fill="#3182CE"/>
      <circle cx="64" cy="64" r="40" fill="none" stroke="white" stroke-width="4"/>
      <circle cx="64" cy="64" r="30" fill="none" stroke="white" stroke-width="4"/>
      <circle cx="64" cy="64" r="20" fill="none" stroke="white" stroke-width="4"/>
      <circle cx="64" cy="64" r="5" fill="white"/>
      <circle cx="45" cy="45" r="8" fill="white" opacity="0.3"/>
    </svg>
  `)}`,
  taskMaster: `data:image/svg+xml,${encodeURIComponent(`
    <svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
      <circle cx="64" cy="64" r="60" fill="#F6AD55"/>
      <circle cx="64" cy="64" r="50" fill="#ED8936"/>
      <path d="M64 30 L80 62 L115 62 L87 82 L95 115 L64 95 L33 115 L41 82 L13 62 L48 62 Z" fill="white"/>
    </svg>
  `)}`,
  superAchiever: `data:image/svg+xml,${encodeURIComponent(`
    <svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
      <circle cx="64" cy="64" r="60" fill="#9F7AEA"/>
      <circle cx="64" cy="64" r="50" fill="#805AD5"/>
      <path d="M64 24 L74 44 L96 48 L80 64 L85 86 L64 76 L43 86 L48 64 L32 48 L54 44 Z" fill="#FFD700"/>
      <circle cx="64" cy="64" r="15" fill="white"/>
    </svg>
  `)}`,
  pointCollector: `data:image/svg+xml,${encodeURIComponent(`
    <svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
      <circle cx="64" cy="64" r="60" fill="#48BB78"/>
      <circle cx="64" cy="64" r="50" fill="#38A169"/>
      <path d="M64 30 L85 52 L110 58 L87 75 L93 100 L64 88 L35 100 L41 75 L18 58 L43 52 Z" fill="#90CDF4"/>
    </svg>
  `)}`,
  pointMaster: `data:image/svg+xml,${encodeURIComponent(`
    <svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
      <circle cx="64" cy="64" r="60" fill="#F6E05E"/>
      <circle cx="64" cy="64" r="50" fill="#D69E2E"/>
      <circle cx="64" cy="64" r="30" fill="#F6AD55"/>
      <path d="M64 34 L76 59 L103 59 L82 75 L88 100 L64 85 L40 100 L46 75 L25 59 L52 59 Z" fill="#FFD700"/>
    </svg>
  `)}`,
  dailyHero: `data:image/svg+xml,${encodeURIComponent(`
    <svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
      <circle cx="64" cy="64" r="60" fill="#FC8181"/>
      <circle cx="64" cy="64" r="50" fill="#E53E3E"/>
      <path d="M64 34 Q80 54 96 34 Q96 54 64 94 Q32 54 32 34 Q48 54 64 34" fill="white"/>
    </svg>
  `)}`,
  weeklyChampion: `data:image/svg+xml,${encodeURIComponent(`
    <svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
      <circle cx="64" cy="64" r="60" fill="#B794F4"/>
      <circle cx="64" cy="64" r="50" fill="#9F7AEA"/>
      <path d="M44 84 L84 84 L84 64 L64 44 L44 64 Z" fill="white"/>
      <circle cx="64" cy="64" r="10" fill="#FFD700"/>
    </svg>
  `)}`,
  monthlyLegend: `data:image/svg+xml,${encodeURIComponent(`
    <svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
      <circle cx="64" cy="64" r="60" fill="#F6AD55"/>
      <circle cx="64" cy="64" r="50" fill="#DD6B20"/>
      <path d="M64 34 L78 48 L98 52 L81 63 L85 83 L64 74 L43 83 L47 63 L30 52 L50 48 Z" fill="#FFD700"/>
      <circle cx="64" cy="64" r="15" fill="#FBD38D"/>
    </svg>
  `)}`,
};

export const ACHIEVEMENTS: CreateAchievementData[] = [
  {
    title: 'First Steps',
    description: 'Complete your first task',
    imageUrl: ACHIEVEMENT_IMAGES.firstSteps,
    condition: {
      type: 'TASKS_COMPLETED',
      value: 1,
    },
  },
  {
    title: 'Task Master',
    description: 'Complete 10 tasks',
    imageUrl: ACHIEVEMENT_IMAGES.taskMaster,
    condition: {
      type: 'TASKS_COMPLETED',
      value: 10,
    },
  },
  {
    title: 'Super Achiever',
    description: 'Complete 50 tasks',
    imageUrl: ACHIEVEMENT_IMAGES.superAchiever,
    condition: {
      type: 'TASKS_COMPLETED',
      value: 50,
    },
  },
  {
    title: 'Point Collector',
    description: 'Earn 100 points',
    imageUrl: ACHIEVEMENT_IMAGES.pointCollector,
    condition: {
      type: 'POINTS_EARNED',
      value: 100,
    },
  },
  {
    title: 'Point Master',
    description: 'Earn 500 points',
    imageUrl: ACHIEVEMENT_IMAGES.pointMaster,
    condition: {
      type: 'POINTS_EARNED',
      value: 500,
    },
  },
  {
    title: 'Daily Hero',
    description: 'Maintain a 3-day streak',
    imageUrl: ACHIEVEMENT_IMAGES.dailyHero,
    condition: {
      type: 'STREAK_MAINTAINED',
      value: 3,
    },
  },
  {
    title: 'Weekly Champion',
    description: 'Maintain a 7-day streak',
    imageUrl: ACHIEVEMENT_IMAGES.weeklyChampion,
    condition: {
      type: 'STREAK_MAINTAINED',
      value: 7,
    },
  },
  {
    title: 'Monthly Legend',
    description: 'Maintain a 30-day streak',
    imageUrl: ACHIEVEMENT_IMAGES.monthlyLegend,
    condition: {
      type: 'STREAK_MAINTAINED',
      value: 30,
    },
  },
];

export const getAchievementImageUrl = (achievementId: string): string => {
  const achievement = ACHIEVEMENTS.find(a => a.title === achievementId);
  return achievement?.imageUrl || ACHIEVEMENT_IMAGES.firstSteps;
};