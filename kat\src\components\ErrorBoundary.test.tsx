import React from 'react';
import { describe, test, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import ErrorBoundary from './ErrorBoundary';

// Component that throws an error
const ThrowError = ({ shouldThrow = false }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <div>Normal render</div>;
};

describe('ErrorBoundary', () => {
  beforeEach(() => {
    // Clear console.error to avoid noise in test output
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  test('should render children when there is no error', () => {
    render(
      <ErrorBoundary>
        <div>Test content</div>
      </ErrorBoundary>
    );

    expect(screen.getByText('Test content')).toBeInTheDocument();
  });

  test('should render error UI when an error occurs', () => {
    const { rerender } = render(
      <ErrorBoundary>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    );

    // Trigger an error
    rerender(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    // Verify error UI is shown
    expect(screen.getByText('Oops!')).toBeInTheDocument();
    expect(
      screen.getByText("Something went wrong. Don't worry, your progress is safe!")
    ).toBeInTheDocument();
    expect(screen.getByText('Try Again')).toBeInTheDocument();
  });

  test('should recover after error when clicking try again', () => {
    const { rerender } = render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    // Verify error UI is shown
    expect(screen.getByText('Oops!')).toBeInTheDocument();

    // Click try again
    fireEvent.click(screen.getByText('Try Again'));

    // Rerender with no error
    rerender(
      <ErrorBoundary>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    );

    // Verify normal content is shown
    expect(screen.getByText('Normal render')).toBeInTheDocument();
  });

  test('should handle nested errors', () => {
    const NestedComponent = () => {
      throw new Error('Nested error');
    };

    render(
      <ErrorBoundary>
        <div>
          <ErrorBoundary>
            <NestedComponent />
          </ErrorBoundary>
          <div>Sibling content</div>
        </div>
      </ErrorBoundary>
    );

    // Inner error boundary should catch error
    expect(screen.getByText('Oops!')).toBeInTheDocument();
    // Sibling content should still render
    expect(screen.getByText('Sibling content')).toBeInTheDocument();
  });

  test('should provide helpful error message', () => {
    const CustomError = () => {
      throw new Error('Custom error message');
    };

    render(
      <ErrorBoundary>
        <CustomError />
      </ErrorBoundary>
    );

    expect(screen.getByText('Oops!')).toBeInTheDocument();
    expect(
      screen.getByText('If the problem persists, try refreshing the page or contact support.')
    ).toBeInTheDocument();
  });
});