import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Avatar,
  Progress,
  Icon,
} from '@chakra-ui/react';
import { FiAward, FiZap } from 'react-icons/fi';
import { useSelector } from 'react-redux';
import { useTheme, useThemeStyles } from '../styles/themes/ThemeContext';
import { RootState } from '../store';
import { usePoints } from '../hooks/usePoints';
import { useStreak } from '../hooks/useStreak';
import { calculateLevel } from '../constants/levels';

const ProfileCard = () => {
  const { currentTheme } = useTheme();
  const { textStyle, accentTextStyle } = useThemeStyles();
  const selectedProfileId = useSelector((state: RootState) => state.profile.selectedProfileId);
  const profile = useSelector((state: RootState) => 
    state.profile.profiles.find(p => p.id === selectedProfileId)
  );

  const { points } = usePoints(selectedProfileId || '');
  const { currentStreak } = useStreak(selectedProfileId || '');

  if (!profile || !selectedProfileId) {
    return null;
  }

  const { level, progress } = calculateLevel(points);

  return (
    <VStack spacing={6} align="stretch">
      <VStack spacing={4} align="center">
        <Avatar
          size="xl"
          name={profile.name}
          src={profile.avatarUrl}
          bg={currentTheme.colors.button.bg}
          color={currentTheme.colors.button.text}
          border="4px solid"
          borderColor={currentTheme.colors.card.border}
          boxShadow={`0 0 20px ${currentTheme.colors.card.glow}`}
        />
        <Text
          fontSize="2xl"
          fontWeight="bold"
          textAlign="center"
          {...textStyle}
        >
          {profile.name}
        </Text>
      </VStack>

      <VStack
        spacing={4}
        bg={currentTheme.colors.stats.bg}
        p={4}
        borderRadius="lg"
        borderWidth="1px"
        borderColor={currentTheme.colors.stats.border}
      >
        <HStack justify="space-between" width="100%">
          <HStack>
            <Icon as={FiAward} color={currentTheme.colors.text.accent} />
            <Text {...textStyle}>Level</Text>
          </HStack>
          <Text {...accentTextStyle} fontWeight="bold">
            {level}
          </Text>
        </HStack>

        <HStack justify="space-between" width="100%">
          <HStack>
            <Icon as={FiZap} color={currentTheme.colors.text.accent} />
            <Text {...textStyle}>Streak</Text>
          </HStack>
          <Text {...accentTextStyle} fontWeight="bold">
            {currentStreak} days
          </Text>
        </HStack>

        <Box width="100%">
          <Text fontSize="sm" mb={2} {...textStyle}>
            Progress to Level {level + 1}
          </Text>
          <Progress
            value={progress}
            size="sm"
            borderRadius="full"
            bg={currentTheme.colors.achievement.locked}
            sx={{
              '& > div': {
                background: `linear-gradient(90deg, 
                  ${currentTheme.colors.achievement.unlocked} 0%, 
                  ${currentTheme.colors.achievement.progress} 100%
                )`,
              },
            }}
          />
        </Box>
      </VStack>
    </VStack>
  );
};

export default ProfileCard;