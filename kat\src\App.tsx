import React, { Suspense, useEffect } from 'react';
import { ChakraProvider, ColorModeScript } from '@chakra-ui/react';
import { Provider as ReduxProvider } from 'react-redux';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { store } from './store';
import theme from './styles/theme';
import Layout from './components/Layout';
import ErrorBoundary from './components/ErrorBoundary';
import LoadingSpinner from './components/LoadingSpinner';
import KeyboardShortcuts from './components/KeyboardShortcuts';
import { LoadingProvider } from './hooks/useLoadingState';
import GlobalLoadingIndicator from './components/GlobalLoadingIndicator';
import AnalyticsConsent from './components/AnalyticsConsent';
import { analytics } from './utils/analytics';
import { ThemeProvider } from './styles/themes/ThemeContext';

// Set up error tracking
const originalConsoleError = console.error;
console.error = (...args) => {
  // Track errors in analytics
  if (args[0] instanceof Error) {
    analytics.trackError(args[0], 'console.error');
  }
  originalConsoleError.apply(console, args);
};

// Error boundary fallback handler
window.onerror = (message, source, lineno, colno, error) => {
  if (error) {
    analytics.trackError(error, 'window.onerror');
  }
  return false;
};

// Lazy load routes with error tracking
const lazyImport = (factory: () => Promise<any>, name: string) => 
  factory().catch(err => {
    analytics.trackError(err, `lazy-load-${name}`);
    throw err;
  });

const Home = React.lazy(() => lazyImport(() => import('./features/home/<USER>'), 'Home'));
const ParentDashboard = React.lazy(() => lazyImport(() => import('./features/parent/ParentDashboard'), 'ParentDashboard'));
const AchievementNotification = React.lazy(() => lazyImport(() => import('./features/achievements/AchievementNotification'), 'AchievementNotification'));

function App() {
  // Track app initialization
  useEffect(() => {
    if (analytics.isEnabled()) {
      analytics.trackEvent('app_initialized', {
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        screenSize: `${window.innerWidth}x${window.innerHeight}`,
      });
    }
  }, []);

  return (
    <React.StrictMode>
      <ErrorBoundary>
        <ChakraProvider theme={theme}>
          <ColorModeScript initialColorMode={theme.config.initialColorMode} />
          <ThemeProvider>
            <LoadingProvider>
              <ReduxProvider store={store}>
                <Router>
                  <Layout>
                    <Suspense fallback={<LoadingSpinner fullScreen message="Loading application..." />}>
                      <Routes>
                        <Route path="/" element={<Home />} />
                        <Route path="/parent" element={<ParentDashboard />} />
                      </Routes>
                      <AchievementNotification />
                      <KeyboardShortcuts />
                    </Suspense>
                    <GlobalLoadingIndicator />
                    <AnalyticsConsent />
                  </Layout>
                </Router>
              </ReduxProvider>
            </LoadingProvider>
          </ThemeProvider>
        </ChakraProvider>
      </ErrorBoundary>
    </React.StrictMode>
  );
}

export default App;