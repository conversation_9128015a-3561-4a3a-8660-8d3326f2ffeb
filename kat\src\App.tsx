import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>ider } from '@chakra-ui/react';
import { Box, Heading, Text, VStack, Button } from '@chakra-ui/react';

function App() {
  return (
    <ChakraProvider>
      <Box minH="100vh" bg="gray.50" p={8}>
        <VStack spacing={6} maxW="md" mx="auto" pt={20}>
          <Heading color="blue.500" size="2xl">
            🎯 KAT App
          </Heading>
          <Text fontSize="lg" textAlign="center" color="gray.600">
            Kids Activity Tracker - Multilingual Support Ready!
          </Text>

          <VStack spacing={4} w="100%">
            <Box p={6} bg="white" borderRadius="lg" shadow="md" w="100%">
              <Heading size="md" mb={3} color="green.500">
                ✅ App is Working!
              </Heading>
              <Text color="gray.600">
                The React application is loading successfully.
              </Text>
            </Box>

            <Box p={6} bg="white" borderRadius="lg" shadow="md" w="100%">
              <Heading size="md" mb={3} color="blue.500">
                🌍 Multilingual Support
              </Heading>
              <Text color="gray.600" mb={3}>
                Ready for implementation with:
              </Text>
              <VStack align="start" spacing={1}>
                <Text>🇺🇸 English</Text>
                <Text>🇵🇱 Polish</Text>
                <Text>🇮🇸 Icelandic</Text>
              </VStack>
            </Box>

            <Button
              colorScheme="blue"
              size="lg"
              onClick={() => window.location.reload()}
            >
              Refresh App
            </Button>
          </VStack>

          <Text fontSize="sm" color="gray.500" textAlign="center">
            Time: {new Date().toLocaleString()}
          </Text>
        </VStack>
      </Box>
    </ChakraProvider>
  );
}

export default App;