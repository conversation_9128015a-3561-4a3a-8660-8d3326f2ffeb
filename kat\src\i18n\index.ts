import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Translation resources - defined inline to avoid import issues
const enTranslations = {
  "common": {
    "loading": "Loading...",
    "error": "Error",
    "success": "Success",
    "cancel": "Cancel",
    "save": "Save",
    "delete": "Delete",
    "edit": "Edit",
    "add": "Add",
    "close": "Close",
    "confirm": "Confirm",
    "back": "Back",
    "next": "Next",
    "done": "Done",
    "yes": "Yes",
    "no": "No",
    "points": "Points",
    "level": "Level"
  },
  "navigation": {
    "home": "Home",
    "tasks": "Tasks",
    "achievements": "Achievements",
    "parent": "Parent",
    "profile": "Profile"
  },
  "language": {
    "title": "Language",
    "select": "Select Language",
    "current": "Current Language",
    "change": "Change Language",
    "changed": "Language changed successfully!"
  }
};

const plTranslations = {
  "common": {
    "loading": "Ładowanie...",
    "error": "Błąd",
    "success": "Sukces",
    "cancel": "Anuluj",
    "save": "Zapisz",
    "delete": "Usuń",
    "edit": "Edytuj",
    "add": "Dodaj",
    "close": "Zamknij",
    "confirm": "Potwierdź",
    "back": "Wstecz",
    "next": "Dalej",
    "done": "Gotowe",
    "yes": "Tak",
    "no": "Nie",
    "points": "Punkty",
    "level": "Poziom"
  },
  "navigation": {
    "home": "Główna",
    "tasks": "Zadania",
    "achievements": "Osiągnięcia",
    "parent": "Rodzic",
    "profile": "Profil"
  },
  "language": {
    "title": "Język",
    "select": "Wybierz Język",
    "current": "Aktualny Język",
    "change": "Zmień Język",
    "changed": "Język został zmieniony pomyślnie!"
  }
};

const isTranslations = {
  "common": {
    "loading": "Hleður...",
    "error": "Villa",
    "success": "Árangur",
    "cancel": "Hætta við",
    "save": "Vista",
    "delete": "Eyða",
    "edit": "Breyta",
    "add": "Bæta við",
    "close": "Loka",
    "confirm": "Staðfesta",
    "back": "Til baka",
    "next": "Næsta",
    "done": "Lokið",
    "yes": "Já",
    "no": "Nei",
    "points": "Stig",
    "level": "Stig"
  },
  "navigation": {
    "home": "Heim",
    "tasks": "Verkefni",
    "achievements": "Afrek",
    "parent": "Foreldri",
    "profile": "Prófíll"
  },
  "language": {
    "title": "Tungumál",
    "select": "Veldu tungumál",
    "current": "Núverandi tungumál",
    "change": "Breyta tungumáli",
    "changed": "Tungumáli breytt!"
  }
};

// Define available languages
export const SUPPORTED_LANGUAGES = {
  en: 'English',
  pl: 'Polski',
  is: 'Íslenska',
} as const;

export type SupportedLanguage = keyof typeof SUPPORTED_LANGUAGES;

// Language detection options
const detectionOptions = {
  // Order of language detection methods
  order: ['localStorage', 'navigator', 'htmlTag'],

  // Keys to look for in localStorage
  lookupLocalStorage: 'kat_language',

  // Cache user language
  caches: ['localStorage'],

  // Don't convert country codes to language codes
  convertDetectedLanguage: (lng: string) => lng.split('-')[0],
};

// Initialize i18next
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    // Language detection
    detection: detectionOptions,

    // Fallback language
    fallbackLng: 'en',

    // Debug mode (disable in production)
    debug: import.meta.env.DEV,

    // Interpolation options
    interpolation: {
      escapeValue: false, // React already escapes values
    },

    // Translation resources
    resources: {
      en: { translation: enTranslations },
      pl: { translation: plTranslations },
      is: { translation: isTranslations },
    },

    // React options
    react: {
      useSuspense: false, // Disable suspense for better error handling
    },
  });

export default i18n;
