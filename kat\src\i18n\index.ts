import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translation files
import enTranslations from './locales/en.json';
import plTranslations from './locales/pl.json';
import isTranslations from './locales/is.json';

// Define available languages
export const SUPPORTED_LANGUAGES = {
  en: 'English',
  pl: '<PERSON>ski',
  is: 'Íslenska',
} as const;

export type SupportedLanguage = keyof typeof SUPPORTED_LANGUAGES;

// Language detection options
const detectionOptions = {
  // Order of language detection methods
  order: ['localStorage', 'navigator', 'htmlTag'],

  // Keys to look for in localStorage
  lookupLocalStorage: 'kat_language',

  // Cache user language
  caches: ['localStorage'],

  // Don't convert country codes to language codes
  convertDetectedLanguage: (lng: string) => lng.split('-')[0],
};

// Initialize i18next
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    // Language detection
    detection: detectionOptions,

    // Fallback language
    fallbackLng: 'en',

    // Debug mode (disable in production)
    debug: import.meta.env.DEV,

    // Interpolation options
    interpolation: {
      escapeValue: false, // React already escapes values
    },

    // Translation resources
    resources: {
      en: { translation: enTranslations },
      pl: { translation: plTranslations },
      is: { translation: isTranslations },
    },

    // React options
    react: {
      useSuspense: false, // Disable suspense for better error handling
    },
  });

export default i18n;
