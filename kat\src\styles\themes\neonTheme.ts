import { CustomTheme } from './types';
import { bounceScale, fadeIn, floatAnimation, glowPulse, popIn, rainbow, rotateGlow, sparkle } from './animations';

export const neonTheme: CustomTheme = {
  name: 'Neon Future',
  colors: {
    bg: {
      primary: '#030308',
      secondary: '#0A0A1A',
      pattern: 'rgba(51, 255, 187, 0.05)',
    },
    card: {
      bg: 'rgba(18, 18, 31, 0.95)',
      border: '#33FFBB',
      hover: 'rgba(24, 24, 40, 0.95)',
      glow: 'rgba(51, 255, 187, 0.4)',
    },
    button: {
      bg: '#FF1A8C',
      hover: '#FF47B0',
      text: '#FFFFFF',
      glow: 'rgba(255, 43, 140, 0.6)',
    },
    text: {
      primary: '#FFFFFF',
      secondary: '#33FFBB',
      accent: '#FF1A8C',
    },
    achievement: {
      locked: 'rgba(51, 255, 187, 0.3)',
      unlocked: '#33FFBB',
      progress: '#FF1A8C',
    },
    stats: {
      bg: 'rgba(18, 18, 31, 0.85)',
      border: '#33FFBB',
      text: '#FFFFFF',
    },
    nav: {
      active: '#FF1A8C',
      inactive: '#33FFBB',
      bg: 'rgba(18, 18, 31, 0.98)',
      indicator: '#FF1A8C',
      glow: 'rgba(255, 43, 140, 0.6)',
    },
  },
  patterns: {
    background: `
      linear-gradient(to bottom, transparent 0%, #0A0A1A 100%),
      radial-gradient(circle at 50% 0%, rgba(51, 255, 187, 0.15) 0%, transparent 60%),
      radial-gradient(circle at 0% 50%, rgba(255, 26, 140, 0.15) 0%, transparent 60%)
    `,
    card: `
      linear-gradient(45deg, rgba(51, 255, 187, 0.15) 0%, rgba(18, 18, 31, 0) 100%)
    `,
    nav: `
      linear-gradient(0deg, rgba(18, 18, 31, 1) 0%, rgba(18, 18, 31, 0.98) 100%)
    `,
  },
  animations: {
    button: `
      animation: ${glowPulse} 2s infinite;
      &:hover {
        animation: ${sparkle} 1s infinite;
        transform: scale(1.05);
      }
    `,
    card: `
      transition: all 0.3s ease;
      &:hover {
        animation: ${bounceScale} 0.5s ease;
        box-shadow: 0 0 20px rgba(51, 255, 187, 0.4);
        transform: translateY(-5px);
      }
    `,
    achievement: `
      transition: all 0.3s ease;
      animation: ${rainbow} 3s infinite;
      &.unlocked {
        animation: ${popIn} 0.5s ease-out, ${floatAnimation} 3s infinite ease-in-out;
      }
      &:hover {
        filter: brightness(120%);
        transform: scale(1.05);
      }
    `,
    nav: `
      transition: all 0.3s ease;
      &.active {
        animation: ${glowPulse} 2s infinite;
      }
      &:hover {
        animation: ${rotateGlow} 2s infinite;
        transform: translateY(-2px);
      }
    `,
  },
  effects: {
    glow: `
      box-shadow: 0 0 15px rgba(51, 255, 187, 0.6),
                 0 0 30px rgba(51, 255, 187, 0.4),
                 0 0 45px rgba(51, 255, 187, 0.2)
    `,
    hover: `
      transform: translateY(-5px) scale(1.02);
      transition: all 0.3s ease;
    `,
    active: `
      transform: scale(0.98);
      transition: all 0.2s ease;
    `,
  },
  config: {
    initialColorMode: 'dark',
    useSystemColorMode: false,
  },
};