import React from 'react';
import { Box, Spinner, Text, VStack } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';

interface LoadingSpinnerProps {
  fullScreen?: boolean;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  message?: string;
  position?: 'fixed' | 'absolute' | 'relative';
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  fullScreen = false,
  size = 'lg',
  message,
  position = 'relative',
}) => {
  const { t } = useTranslation();
  const containerStyles = fullScreen
    ? {
        position: 'fixed' as const,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.4)',
        zIndex: 'overlay',
        backdropFilter: 'blur(4px)',
      }
    : {
        position,
        width: '100%',
        height: '100%',
        minHeight: '200px',
      };

  return (
    <Box
      {...containerStyles}
      display="flex"
      alignItems="center"
      justifyContent="center"
      role="alert"
      aria-busy="true"
      aria-label={message || t('common.loading')}
    >
      <VStack spacing={4}>
        <Spinner
          thickness="4px"
          speed="0.65s"
          emptyColor="gray.200"
          color="primary.500"
          size={size}
        />
        {message && (
          <Text
            color={fullScreen ? 'white' : 'gray.600'}
            fontSize={size === 'xs' || size === 'sm' ? 'sm' : 'md'}
            fontWeight="medium"
            textAlign="center"
            maxWidth="300px"
          >
            {message}
          </Text>
        )}
      </VStack>
    </Box>
  );
};

export default LoadingSpinner;