{"name": "kat", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc --noEmit && vite build --config vite.config.prod.ts", "build:analyze": "ANALYZE=true npm run build", "preview": "vite preview", "typecheck": "tsc --noEmit", "test": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write \"src/**/*.{ts,tsx}\"", "precommit": "npm run typecheck && npm run lint && npm run test", "prepare": "husky install", "predeploy": "npm run build", "deploy": "gh-pages -d dist"}, "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^2.8.2", "@chakra-ui/system": "^2.6.2", "@chakra-ui/theme-tools": "^2.1.2", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@reduxjs/toolkit": "^2.6.0", "canvas-confetti": "^1.9.3", "framer-motion": "^6.5.1", "idb": "^8.0.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "react-router-dom": "^7.2.0", "uuid": "^11.1.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/canvas-confetti": "^1.9.0", "@types/node": "^22.13.5", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "gh-pages": "^6.1.1", "husky": "^8.0.3", "jsdom": "^26.0.0", "lint-staged": "^15.2.0", "prettier": "^3.2.0", "rollup-plugin-visualizer": "^5.12.0", "typescript": "~5.7.2", "vite": "^6.2.0", "vite-plugin-pwa": "^0.17.5", "vitest": "^3.0.7"}, "lint-staged": {"src/**/*.{ts,tsx}": ["eslint --fix", "prettier --write"]}, "homepage": "https://your-username.github.io/kat"}