import { CustomTheme } from './types';

export const candyTheme: CustomTheme = {
  name: 'Sweet Candy',
  colors: {
    bg: {
      primary: '#FFE6F2',
      secondary: '#FFCCE6',
      pattern: 'rgba(255, 182, 193, 0.2)',
    },
    card: {
      bg: '#FFFFFF',
      border: '#FFB6C1',
      hover: '#FFF0F5',
      glow: 'rgba(255, 182, 193, 0.4)',
    },
    button: {
      bg: '#FF69B4',
      hover: '#FF8BBD',
      text: '#FFFFFF',
      glow: 'rgba(255, 105, 180, 0.4)',
    },
    text: {
      primary: '#FF69B4',
      secondary: '#FF8BBD',
      accent: '#FFA07A',
    },
    achievement: {
      locked: 'rgba(255, 182, 193, 0.3)',
      unlocked: '#FF69B4',
      progress: '#FFA07A',
    },
    stats: {
      bg: '#FFF0F5',
      border: '#FFB6C1',
      text: '#FF69B4',
    },
    nav: {
      active: '#FF69B4',
      inactive: '#FF8BBD',
      bg: 'rgba(255, 255, 255, 0.95)',
      indicator: '#FF69B4',
      glow: 'rgba(255, 105, 180, 0.4)',
    },
  },
  patterns: {
    background: `
      radial-gradient(circle at 10% 20%, rgba(255, 182, 193, 0.2) 0%, transparent 20%),
      radial-gradient(circle at 90% 50%, rgba(255, 160, 122, 0.2) 0%, transparent 20%),
      radial-gradient(circle at 50% 80%, rgba(255, 105, 180, 0.2) 0%, transparent 20%),
      url("data:image/svg+xml,%3Csvg width='52' height='26' viewBox='0 0 52 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ff69b4' fill-opacity='0.1'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
    `,
    card: `
      linear-gradient(45deg, rgba(255, 182, 193, 0.1) 0%, rgba(255, 228, 225, 0.1) 100%)
    `,
    nav: `
      linear-gradient(0deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%)
    `,
  },
  animations: {
    button: `
      @keyframes bounce {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05) rotate(2deg); }
      }
    `,
    card: `
      @keyframes wobble {
        0% { transform: translateY(0) rotate(0); }
        25% { transform: translateY(-5px) rotate(1deg); }
        75% { transform: translateY(5px) rotate(-1deg); }
        100% { transform: translateY(0) rotate(0); }
      }
    `,
    achievement: `
      @keyframes sparkle {
        0%, 100% { filter: brightness(100%) saturate(100%); }
        50% { filter: brightness(120%) saturate(150%); }
      }
    `,
    nav: `
      @keyframes navSparkle {
        0%, 100% { 
          box-shadow: 0 -2px 10px rgba(255, 105, 180, 0.2);
          border-top-color: rgba(255, 105, 180, 0.4);
        }
        50% { 
          box-shadow: 0 -2px 15px rgba(255, 105, 180, 0.4);
          border-top-color: rgba(255, 105, 180, 0.6);
        }
      }
    `,
  },
  effects: {
    glow: 'box-shadow: 0 0 15px',
    hover: 'transform: scale(1.02) rotate(1deg)',
    active: 'transform: scale(0.98) rotate(-1deg)',
  },
  config: {
    initialColorMode: 'light',
    useSystemColorMode: false,
  },
};