import React, { useRef, useEffect } from 'react';
import {
  Box,
  Text,
  HStack,
  VStack,
  Icon,
  Tooltip,
} from '@chakra-ui/react';
import { FiZap } from 'react-icons/fi';
import { motion } from 'framer-motion';
import { playSound } from '../utils/sounds';

const MotionIcon = motion(Icon);

interface StreakTrackerProps {
  currentStreak: number;
  bestStreak: number;
}

const StreakTracker: React.FC<StreakTrackerProps> = ({ currentStreak, bestStreak }) => {
  const prevStreakRef = useRef(currentStreak);

  useEffect(() => {
    if (currentStreak > prevStreakRef.current) {
      playSound('streak');
    }
    prevStreakRef.current = currentStreak;
  }, [currentStreak]);

  return (
    <Box
      bg="white"
      p={4}
      borderRadius="lg"
      boxShadow="sm"
      border="1px solid"
      borderColor="gray.100"
    >
      <VStack spacing={2} align="stretch">
        <HStack spacing={4} justify="center">
          <Tooltip label="Current Streak" placement="top" aria-label="Current daily streak count">
            <VStack spacing={1} role="status" aria-label="Current streak">
              <MotionIcon
                as={FiZap}
                color="orange.500"
                boxSize={6}
                animate={currentStreak > 0 ? {
                  scale: [1, 1.2, 1],
                  rotate: [0, 10, -10, 0],
                } : {}}
                transition={{
                  duration: 0.5,
                  repeat: Infinity,
                  repeatType: "reverse",
                }}
              />
              <Text
                fontSize="xl"
                fontWeight="bold"
                color="orange.500"
              >
                {currentStreak}
              </Text>
              <Text fontSize="xs" color="gray.500">
                Current
              </Text>
            </VStack>
          </Tooltip>

          <Tooltip label="Best Streak" placement="top" aria-label="Best streak achieved">
            <VStack spacing={1} role="status" aria-label="Best streak record">
              <Icon
                as={FiZap}
                color="purple.500"
                boxSize={6}
              />
              <Text
                fontSize="xl"
                fontWeight="bold"
                color="purple.500"
              >
                {bestStreak}
              </Text>
              <Text fontSize="xs" color="gray.500">
                Best
              </Text>
            </VStack>
          </Tooltip>
        </HStack>

        <Text
          textAlign="center"
          fontSize="sm"
          color="gray.600"
          mt={2}
          role="status"
          aria-live="polite"
        >
          {currentStreak > 0
            ? `${currentStreak} day${currentStreak === 1 ? '' : 's'} streak! Keep it up! ⚡`
            : 'Complete tasks daily to build your streak! ⚡'}
        </Text>
      </VStack>
    </Box>
  );
};

export default StreakTracker;