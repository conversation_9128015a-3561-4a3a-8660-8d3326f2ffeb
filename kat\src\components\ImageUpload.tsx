import { useRef, useState } from 'react';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Image,
  Input,
  VStack,
  useToast,
  FormErrorMessage,
} from '@chakra-ui/react';
import { processImageUpload } from '../utils/imageUpload';

interface ImageUploadProps {
  onImageSelect: (imageData: string) => void;
  currentImage?: string;
}

const ImageUpload = ({ onImageSelect, currentImage }: ImageUploadProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const toast = useToast();
  const [error, setError] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentImage || null);

  const handleImageChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const result = await processImageUpload(file);
      
      if (result.error) {
        setError(result.error);
        toast({
          title: 'Error',
          description: result.error,
          status: 'error',
          duration: 3000,
        });
        return;
      }

      if (result.data) {
        setPreviewUrl(result.data);
        onImageSelect(result.data);
        setError(null);
      }
    } catch (error) {
      setError('Failed to process image. Please try again.');
      toast({
        title: 'Error',
        description: 'Failed to process image. Please try again.',
        status: 'error',
        duration: 3000,
      });
    }
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <FormControl isInvalid={!!error}>
      <FormLabel>Profile Picture</FormLabel>
      <VStack spacing={4} align="center">
        <Box
          w="150px"
          h="150px"
          borderRadius="full"
          overflow="hidden"
          position="relative"
          cursor="pointer"
          onClick={handleClick}
          border="2px dashed"
          borderColor={previewUrl ? 'transparent' : 'gray.300'}
          _hover={{
            borderColor: previewUrl ? 'transparent' : 'blue.500',
          }}
        >
          {previewUrl ? (
            <Image
              src={previewUrl}
              alt="Profile preview"
              w="100%"
              h="100%"
              objectFit="cover"
            />
          ) : (
            <Box
              w="100%"
              h="100%"
              display="flex"
              alignItems="center"
              justifyContent="center"
              color="gray.500"
              fontSize="sm"
              textAlign="center"
              p={2}
            >
              Click to upload image
            </Box>
          )}
        </Box>

        <Input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageChange}
          display="none"
          data-testid="file-input"
        />

        <Button
          size="sm"
          onClick={handleClick}
          colorScheme={previewUrl ? 'gray' : 'blue'}
        >
          {previewUrl ? 'Change Image' : 'Upload Image'}
        </Button>

        {error && (
          <FormErrorMessage data-testid="error-message">
            {error}
          </FormErrorMessage>
        )}
      </VStack>
    </FormControl>
  );
};

export default ImageUpload;