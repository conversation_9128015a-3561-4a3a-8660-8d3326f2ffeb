export interface Achievement {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  condition: AchievementCondition;
}

export type AchievementCondition = {
  type: 'TASKS_COMPLETED' | 'POINTS_EARNED' | 'STREAK_MAINTAINED';
  value: number;
};

export interface UserAchievement {
  profileId: string;
  achievementId: string;
  unlockedAt: number;
}

export type CreateAchievementData = Omit<Achievement, 'id'>;

// Helper to convert CreateAchievementData to Achievement
export const createAchievement = (data: CreateAchievementData): Achievement => ({
  ...data,
  id: data.title.toLowerCase().replace(/\s+/g, '-'),
});